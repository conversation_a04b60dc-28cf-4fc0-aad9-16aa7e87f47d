{"_args": [[{"raw": "spdx-license-ids@^1.0.2", "scope": null, "escapedName": "spdx-license-ids", "name": "spdx-license-ids", "rawSpec": "^1.0.2", "spec": ">=1.0.2 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/spdx-correct"]], "_from": "spdx-license-ids@>=1.0.2 <2.0.0", "_id": "spdx-license-ids@1.2.2", "_inCache": true, "_location": "/spdx-license-ids", "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/spdx-license-ids-1.2.2.tgz_1469529975605_0.35518706892617047"}, "_npmUser": {"name": "shinnn", "email": "<EMAIL>"}, "_npmVersion": "3.10.5", "_phantomChildren": {}, "_requested": {"raw": "spdx-license-ids@^1.0.2", "scope": null, "escapedName": "spdx-license-ids", "name": "spdx-license-ids", "rawSpec": "^1.0.2", "spec": ">=1.0.2 <2.0.0", "type": "range"}, "_requiredBy": ["/spdx-correct"], "_resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz", "_shasum": "c9df7a3424594ade6bd11900d596696dc06bac57", "_shrinkwrap": null, "_spec": "spdx-license-ids@^1.0.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/spdx-correct", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shinnn"}, "bugs": {"url": "https://github.com/shinnn/spdx-license-ids/issues"}, "dependencies": {}, "description": "A list of SPDX license identifiers", "devDependencies": {"@shinnn/eslint-config-node": "^3.0.0", "chalk": "^1.1.3", "eslint": "^3.1.1", "get-spdx-license-ids": "^1.0.0", "istanbul": "^0.4.4", "loud-rejection": "^1.6.0", "rimraf-promise": "^2.0.0", "stringify-object": "^2.4.0", "tap-spec": "^4.1.1", "tape": "^4.6.0", "write-file-atomically": "1.0.0"}, "directories": {}, "dist": {"shasum": "c9df7a3424594ade6bd11900d596696dc06bac57", "tarball": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz"}, "files": ["spdx-license-ids.json"], "gitHead": "70e2541bf04b4fbef4c5df52c581a1861fd355b2", "homepage": "https://github.com/shinnn/spdx-license-ids#readme", "keywords": ["spdx", "license", "licenses", "id", "identifier", "identifiers", "json", "array", "oss", "browser", "client-side"], "license": "Unlicense", "main": "spdx-license-ids.json", "maintainers": [{"name": "shinnn", "email": "<EMAIL>"}], "name": "spdx-license-ids", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/shinnn/spdx-license-ids.git"}, "scripts": {"build": "node --strong_mode build.js", "coverage": "node --strong_mode node_modules/.bin/istanbul cover test.js", "lint": "eslint --config @shinnn/node --env browser --ignore-path .gitignore .", "pretest": "${npm_package_scripts_build} && ${npm_package_scripts_lint}", "test": "node --strong_mode test.js | tap-spec"}, "version": "1.2.2"}