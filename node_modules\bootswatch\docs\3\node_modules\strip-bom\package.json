{"_args": [[{"raw": "strip-bom@^2.0.0", "scope": null, "escapedName": "strip-bom", "name": "strip-bom", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/load-json-file"]], "_from": "strip-bom@>=2.0.0 <3.0.0", "_id": "strip-bom@2.0.0", "_inCache": true, "_location": "/strip-bom", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.11.2", "_phantomChildren": {}, "_requested": {"raw": "strip-bom@^2.0.0", "scope": null, "escapedName": "strip-bom", "name": "strip-bom", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/load-json-file"], "_resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "_shasum": "6219a85616520491f35788bdbf1447a99c7e6b0e", "_shrinkwrap": null, "_spec": "strip-bom@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/load-json-file", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "dependencies": {"is-utf8": "^0.2.0"}, "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "devDependencies": {"mocha": "*"}, "directories": {}, "dist": {"shasum": "6219a85616520491f35788bdbf1447a99c7e6b0e", "tarball": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "851b9c126dba9561cc14ef3dc2634dcc11df4d11", "homepage": "https://github.com/sindresorhus/strip-bom", "keywords": ["bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "buffer", "string"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "strip-bom", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "scripts": {"test": "mocha"}, "version": "2.0.0"}