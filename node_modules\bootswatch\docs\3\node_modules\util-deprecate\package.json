{"_args": [[{"raw": "util-deprecate@~1.0.1", "scope": null, "escapedName": "util-deprecate", "name": "util-deprecate", "rawSpec": "~1.0.1", "spec": ">=1.0.1 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream"]], "_from": "util-deprecate@>=1.0.1 <1.1.0", "_id": "util-deprecate@1.0.2", "_inCache": true, "_location": "/util-deprecate", "_nodeVersion": "4.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_npmVersion": "2.14.4", "_phantomChildren": {}, "_requested": {"raw": "util-deprecate@~1.0.1", "scope": null, "escapedName": "util-deprecate", "name": "util-deprecate", "rawSpec": "~1.0.1", "spec": ">=1.0.1 <1.1.0", "type": "range"}, "_requiredBy": ["/readable-stream"], "_resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "_shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "_shrinkwrap": null, "_spec": "util-deprecate@~1.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "browser": "browser.js", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "dependencies": {}, "description": "The Node.js `util.deprecate()` function with browser support", "devDependencies": {}, "directories": {}, "dist": {"shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "tarball": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"}, "gitHead": "475fb6857cd23fafff20c1be846c1350abf8e6d4", "homepage": "https://github.com/TooTallNate/util-deprecate", "keywords": ["util", "deprecate", "browserify", "browser", "node"], "license": "MIT", "main": "node.js", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "name": "util-deprecate", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.2"}