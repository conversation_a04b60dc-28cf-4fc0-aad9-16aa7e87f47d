{"_args": [[{"raw": "wide-align@^1.1.0", "scope": null, "escapedName": "wide-align", "name": "wide-align", "rawSpec": "^1.1.0", "spec": ">=1.1.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/gauge"]], "_from": "wide-align@>=1.1.0 <2.0.0", "_id": "wide-align@1.1.2", "_inCache": true, "_location": "/wide-align", "_nodeVersion": "7.7.4", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/wide-align-1.1.2.tgz_1494527486997_0.9166653461288661"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "_phantomChildren": {}, "_requested": {"raw": "wide-align@^1.1.0", "scope": null, "escapedName": "wide-align", "name": "wide-align", "rawSpec": "^1.1.0", "spec": ">=1.1.0 <2.0.0", "type": "range"}, "_requiredBy": ["/gauge"], "_resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.2.tgz", "_shasum": "571e0f1b0604636ebc0dfc21b0339bbe31341710", "_shrinkwrap": null, "_spec": "wide-align@^1.1.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/gauge", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "dependencies": {"string-width": "^1.0.2"}, "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "devDependencies": {"tap": "^10.3.2"}, "directories": {}, "dist": {"integrity": "sha512-ijDLlyQ7s6x1JgCLur53osjm/UXUYD9+0PbYKrBsYisYXzCxN+HC3mYDNy/dWdmf3AwqwU3CXwDCvsNgGK1S0w==", "shasum": "571e0f1b0604636ebc0dfc21b0339bbe31341710", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.2.tgz"}, "files": ["align.js"], "gitHead": "f904295d03f73ddce10c2c1b0808513ebb5525c6", "homepage": "https://github.com/iarna/wide-align#readme", "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "license": "ISC", "main": "align.js", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "name": "wide-align", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "scripts": {"test": "tap --coverage test/*.js", "version": "perl -pi -e 's/^(  \"version\": $ENV{npm_config_node_version}\").*?\",/$1abc\",/' package-lock.json ; git add package-lock.json"}, "version": "1.1.2"}