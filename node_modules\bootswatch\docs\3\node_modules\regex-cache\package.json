{"_args": [[{"raw": "regex-cache@^0.4.2", "scope": null, "escapedName": "regex-cache", "name": "regex-cache", "rawSpec": "^0.4.2", "spec": ">=0.4.2 <0.5.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/micromatch"]], "_from": "regex-cache@>=0.4.2 <0.5.0", "_id": "regex-cache@0.4.4", "_inCache": true, "_location": "/regex-cache", "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regex-cache-0.4.4.tgz_1504279132002_0.2753396413754672"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "_npmVersion": "5.3.0", "_phantomChildren": {}, "_requested": {"raw": "regex-cache@^0.4.2", "scope": null, "escapedName": "regex-cache", "name": "regex-cache", "rawSpec": "^0.4.2", "spec": ">=0.4.2 <0.5.0", "type": "range"}, "_requiredBy": ["/micromatch"], "_resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "_shasum": "75bdc58a2a1496cec48a12835bc54c8d562336dd", "_shrinkwrap": null, "_spec": "regex-cache@^0.4.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/regex-cache/issues"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://kolarik.sk"}], "dependencies": {"is-equal-shallow": "^0.1.3"}, "description": "Memoize the results of a call to the RegExp constructor, avoiding repetitious runtime compilation of the same string and options, resulting in surprising performance improvements.", "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.1.5", "gulp-format-md": "^0.1.7", "micromatch": "^2.3.7", "should": "^8.3.0"}, "directories": {}, "dist": {"integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "shasum": "75bdc58a2a1496cec48a12835bc54c8d562336dd", "tarball": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "e5ced08e45d2cc2d286a9e7b5a574963f6577712", "homepage": "https://github.com/jonschlinkert/regex-cache", "keywords": ["cache", "expression", "regex", "regexp", "regular", "regular expression", "store", "to-regex"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "name": "regex-cache", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/regex-cache.git"}, "scripts": {"benchmarks": "node benchmark", "test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "reflinks": ["verb"], "lint": {"reflinks": true}}, "version": "0.4.4"}