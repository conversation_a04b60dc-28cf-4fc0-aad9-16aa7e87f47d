{"_args": [[{"raw": "y18n@^3.2.1", "scope": null, "escapedName": "y18n", "name": "y18n", "rawSpec": "^3.2.1", "spec": ">=3.2.1 <4.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/yargs"]], "_from": "y18n@>=3.2.1 <4.0.0", "_id": "y18n@3.2.1", "_inCache": true, "_location": "/y18n", "_nodeVersion": "3.2.0", "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/y18n-3.2.1.tgz_1458191070611_0.9606689948122948"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "3.3.0", "_phantomChildren": {}, "_requested": {"raw": "y18n@^3.2.1", "scope": null, "escapedName": "y18n", "name": "y18n", "rawSpec": "^3.2.1", "spec": ">=3.2.1 <4.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz", "_shasum": "6d15fba884c08679c0d77e88e7759e811e07fa41", "_shrinkwrap": null, "_spec": "y18n@^3.2.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "dependencies": {}, "description": "the bare-bones internationalization library used by yargs", "devDependencies": {"chai": "^3.4.1", "coveralls": "^2.11.6", "mocha": "^2.3.4", "nyc": "^6.1.1", "rimraf": "^2.5.0", "standard": "^5.4.1"}, "directories": {}, "dist": {"shasum": "6d15fba884c08679c0d77e88e7759e811e07fa41", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz"}, "files": ["index.js"], "gitHead": "34d6ad7bfeac67721ccbcf3bbcc761f33d787c90", "homepage": "https://github.com/yargs/y18n", "keywords": ["i18n", "internationalization", "yargs"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "name": "y18n", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "test": "nyc mocha"}, "version": "3.2.1"}