{"_args": [[{"raw": "stream-combiner@~0.0.4", "scope": null, "escapedName": "stream-combiner", "name": "stream-combiner", "rawSpec": "~0.0.4", "spec": ">=0.0.4 <0.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream"]], "_from": "stream-combiner@>=0.0.4 <0.1.0", "_id": "stream-combiner@0.0.4", "_inCache": true, "_location": "/stream-combiner", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_npmVersion": "1.3.11", "_phantomChildren": {}, "_requested": {"raw": "stream-combiner@~0.0.4", "scope": null, "escapedName": "stream-combiner", "name": "stream-combiner", "rawSpec": "~0.0.4", "spec": ">=0.0.4 <0.1.0", "type": "range"}, "_requiredBy": ["/event-stream"], "_resolved": "https://registry.npmjs.org/stream-combiner/-/stream-combiner-0.0.4.tgz", "_shasum": "4d5e433c185261dde623ca3f44c586bcf5c4ad14", "_shrinkwrap": null, "_spec": "stream-combiner@~0.0.4", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream", "author": {"name": "'<PERSON>'", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/stream-combiner/issues"}, "dependencies": {"duplexer": "~0.1.1"}, "description": "<img src=https://secure.travis-ci.org/dominictarr/stream-combiner.png?branch=master>", "devDependencies": {"event-stream": "~3.0.7", "tape": "~2.3.0"}, "directories": {}, "dist": {"shasum": "4d5e433c185261dde623ca3f44c586bcf5c4ad14", "tarball": "https://registry.npmjs.org/stream-combiner/-/stream-combiner-0.0.4.tgz"}, "homepage": "https://github.com/dominictarr/stream-combiner", "license": "MIT", "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "name": "stream-combiner", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/dominictarr/stream-combiner.git"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "version": "0.0.4"}