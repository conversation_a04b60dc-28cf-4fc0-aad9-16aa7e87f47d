{"_args": [[{"raw": "string.prototype.padend@^3.0.0", "scope": null, "escapedName": "string.prototype.padend", "name": "string.prototype.padend", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/npm-run-all"]], "_from": "string.prototype.padend@>=3.0.0 <4.0.0", "_id": "string.prototype.padend@3.0.0", "_inCache": true, "_location": "/string.prototype.padend", "_nodeVersion": "5.0.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmVersion": "3.3.10", "_phantomChildren": {}, "_requested": {"raw": "string.prototype.padend@^3.0.0", "scope": null, "escapedName": "string.prototype.padend", "name": "string.prototype.padend", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "_requiredBy": ["/npm-run-all"], "_resolved": "https://registry.npmjs.org/string.prototype.padend/-/string.prototype.padend-3.0.0.tgz", "_shasum": "f3aaef7c1719f170c5eab1c32bf780d96e21f2f0", "_shrinkwrap": null, "_spec": "string.prototype.padend@^3.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/npm-run-all", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/es-shims/String.prototype.padEnd/issues"}, "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.4.3", "function-bind": "^1.0.2"}, "description": "ES7 spec-compliant String.prototype.padEnd shim.", "devDependencies": {"@es-shims/api": "^1.0.0", "@ljharb/eslint-config": "^1.6.0", "covert": "^1.1.0", "eslint": "^1.9.0", "jscs": "^2.5.1", "nsp": "^2.0.2", "tape": "^4.2.2"}, "directories": {}, "dist": {"shasum": "f3aaef7c1719f170c5eab1c32bf780d96e21f2f0", "tarball": "https://registry.npmjs.org/string.prototype.padend/-/string.prototype.padend-3.0.0.tgz"}, "engines": {"node": ">= 0.4"}, "gitHead": "61b24e1bf9645e2f9a361f0c81222c6083f038b6", "homepage": "https://github.com/es-shims/String.prototype.padEnd#readme", "keywords": ["String.prototype.padRight", "String.prototype.padEnd", "string", "ES7", "shim", "trim", "padLeft", "padRight", "padStart", "padEnd", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "name": "string.prototype.padend", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.padEnd.git"}, "scripts": {"coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "eslint": "eslint test/*.js *.js", "jscs": "jscs test/*.js *.js", "lint": "npm run jscs && npm run eslint", "security": "nsp check", "test": "npm run lint && npm run tests-only && npm run security", "test:module": "node test/index.js", "test:shimmed": "node test/shimmed.js", "tests-only": "es-shim-api --bound && npm run test:shimmed && npm run test:module"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "3.0.0"}