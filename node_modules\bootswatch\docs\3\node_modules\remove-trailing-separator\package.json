{"_args": [[{"raw": "remove-trailing-separator@^1.0.1", "scope": null, "escapedName": "remove-trailing-separator", "name": "remove-trailing-separator", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-path"]], "_from": "remove-trailing-separator@>=1.0.1 <2.0.0", "_id": "remove-trailing-separator@1.1.0", "_inCache": true, "_location": "/remove-trailing-separator", "_nodeVersion": "7.6.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remove-trailing-separator-1.1.0.tgz_1502872321750_0.834721862571314"}, "_npmUser": {"name": "da<PERSON>in", "email": "<EMAIL>"}, "_npmVersion": "4.2.0", "_phantomChildren": {}, "_requested": {"raw": "remove-trailing-separator@^1.0.1", "scope": null, "escapedName": "remove-trailing-separator", "name": "remove-trailing-separator", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "_requiredBy": ["/normalize-path"], "_resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "_shasum": "c24bce2a283adad5bc3f58e0d48249b92379d8ef", "_shrinkwrap": null, "_spec": "remove-trailing-separator@^1.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-path", "author": {"name": "da<PERSON>in"}, "bugs": {"url": "https://github.com/darsain/remove-trailing-separator/issues"}, "dependencies": {}, "description": "Removes separators from the end of the string.", "devDependencies": {"ava": "^0.16.0", "coveralls": "^2.11.14", "nyc": "^8.3.0", "xo": "^0.16.0"}, "directories": {}, "dist": {"shasum": "c24bce2a283adad5bc3f58e0d48249b92379d8ef", "tarball": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"}, "files": ["index.js"], "gitHead": "f4e8acca09106efeef5a5164f1ad2192fe97fd69", "homepage": "https://github.com/darsain/remove-trailing-separator#readme", "keywords": ["remove", "strip", "trailing", "separator"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "da<PERSON>in", "email": "<EMAIL>"}], "name": "remove-trailing-separator", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/darsain/remove-trailing-separator.git"}, "scripts": {"lint": "xo", "pretest": "npm run lint", "report": "nyc report --reporter=html", "test": "nyc ava"}, "version": "1.1.0"}