{"_args": [[{"raw": "spdx-correct@~1.0.0", "scope": null, "escapedName": "spdx-correct", "name": "spdx-correct", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/validate-npm-package-license"]], "_from": "spdx-correct@>=1.0.0 <1.1.0", "_id": "spdx-correct@1.0.2", "_inCache": true, "_location": "/spdx-correct", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.3.6", "_phantomChildren": {}, "_requested": {"raw": "spdx-correct@~1.0.0", "scope": null, "escapedName": "spdx-correct", "name": "spdx-correct", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "_requiredBy": ["/validate-npm-package-license"], "_resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-1.0.2.tgz", "_shasum": "4b3073d933ff51f3912f03ac5519498a4150db40", "_shrinkwrap": null, "_spec": "spdx-correct@~1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/validate-npm-package-license", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, "bugs": {"url": "https://github.com/kemitchell/spdx-correct.js/issues"}, "dependencies": {"spdx-license-ids": "^1.0.2"}, "description": "correct invalid SPDX identifiers", "devDependencies": {"defence-cli": "^1.0.1", "replace-require-self": "^1.0.0", "spdx-expression-parse": "^1.0.0", "tape": "~4.0.0"}, "directories": {}, "dist": {"shasum": "4b3073d933ff51f3912f03ac5519498a4150db40", "tarball": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-1.0.2.tgz"}, "gitHead": "8430a3ad521e1455208db33faafcb79c7b074236", "homepage": "https://github.com/kemitchell/spdx-correct.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata"], "license": "Apache-2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "name": "spdx-correct", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/spdx-correct.js.git"}, "scripts": {"test": "defence README.md | replace-require-self | node && tape *.test.js"}, "version": "1.0.2"}