{"_args": [[{"raw": "spdx-expression-parse@~1.0.0", "scope": null, "escapedName": "spdx-expression-parse", "name": "spdx-expression-parse", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/validate-npm-package-license"]], "_from": "spdx-expression-parse@>=1.0.0 <1.1.0", "_id": "spdx-expression-parse@1.0.4", "_inCache": true, "_location": "/spdx-expression-parse", "_nodeVersion": "4.6.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/spdx-expression-parse-1.0.4.tgz_1475698361593_0.7478717286139727"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.8", "_phantomChildren": {}, "_requested": {"raw": "spdx-expression-parse@~1.0.0", "scope": null, "escapedName": "spdx-expression-parse", "name": "spdx-expression-parse", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "_requiredBy": ["/validate-npm-package-license"], "_resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz", "_shasum": "9bdf2f20e1f40ed447fbe273266191fced51626c", "_shrinkwrap": null, "_spec": "spdx-expression-parse@~1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/validate-npm-package-license", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kemitchell.com"}, "bugs": {"url": "https://github.com/kemitchell/spdx-expression-parse.js/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://cscott.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "parse SPDX license expressions", "devDependencies": {"defence-cli": "^1.0.1", "jison": "^0.4.15", "replace-require-self": "^1.0.0", "spdx-exceptions": "^1.0.4", "spdx-license-ids": "^1.0.0", "standard": "^8.0.0"}, "directories": {}, "dist": {"shasum": "9bdf2f20e1f40ed447fbe273266191fced51626c", "tarball": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz"}, "files": ["AUTHORS", "index.js", "parser.js"], "gitHead": "326b222ed9e89e9ef472656e9970649b9ee4e8f3", "homepage": "https://github.com/kemitchell/spdx-expression-parse.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata", "package", "package.json", "standards"], "license": "(MIT AND CC-BY-3.0)", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "spdx-expression-parse", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/spdx-expression-parse.js.git"}, "scripts": {"lint": "standard", "prepublish": "node generate-parser.js > parser.js", "pretest": "npm run prepublish", "test": "defence -i javascript README.md | replace-require-self | node"}, "version": "1.0.4"}