'use strict';

exports.__esModule = true;

var _previousMap = require('./previous-map');

var _previousMap2 = _interopRequireDefault(_previousMap);

var _path = require('path');

var _path2 = _interopRequireDefault(_path);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var sequence = 0;

var Input = function () {
    function Input(css) {
        var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

        _classCallCheck(this, Input);

        this.css = css.toString();

        if (this.css[0] === '\uFEFF' || this.css[0] === '\uFFFE') {
            this.css = this.css.slice(1);
        }

        if (opts.from) this.file = _path2.default.resolve(opts.from);

        var map = new _previousMap2.default(this.css, opts, this.id);
        if (map.text) {
            this.map = map;
            var file = map.consumer().file;
            if (!this.file && file) this.file = this.mapResolve(file);
        }

        if (this.file) {
            this.from = this.file;
        } else {
            sequence += 1;
            this.id = '<input css ' + sequence + '>';
            this.from = this.id;
        }
        if (this.map) this.map.file = this.from;
    }

    Input.prototype.mapResolve = function mapResolve(file) {
        return _path2.default.resolve(this.map.consumer().sourceRoot || '.', file);
    };

    return Input;
}();

exports.default = Input;