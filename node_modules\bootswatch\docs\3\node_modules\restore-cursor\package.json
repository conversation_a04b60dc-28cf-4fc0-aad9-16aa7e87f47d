{"_args": [[{"raw": "restore-cursor@^2.0.0", "scope": null, "escapedName": "restore-cursor", "name": "restore-cursor", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/cli-cursor"]], "_from": "restore-cursor@>=2.0.0 <3.0.0", "_id": "restore-cursor@2.0.0", "_inCache": true, "_location": "/restore-cursor", "_nodeVersion": "4.6.2", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/restore-cursor-2.0.0.tgz_1483989430842_0.5384121846873313"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.15.11", "_phantomChildren": {"mimic-fn": "1.1.0"}, "_requested": {"raw": "restore-cursor@^2.0.0", "scope": null, "escapedName": "restore-cursor", "name": "restore-cursor", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/cli-cursor"], "_resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "_shasum": "9f7ee287f82fd326d4fd162923d62129eee0dfaf", "_shrinkwrap": null, "_spec": "restore-cursor@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/cli-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "description": "Gracefully restore the CLI cursor on exit", "devDependencies": {}, "directories": {}, "dist": {"shasum": "9f7ee287f82fd326d4fd162923d62129eee0dfaf", "tarball": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz"}, "engines": {"node": ">=4"}, "files": ["index.js"], "gitHead": "0a0d317b421cb7f89d496ad95e2936b781b8f952", "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "restore-cursor", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/restore-cursor.git"}, "scripts": {}, "version": "2.0.0"}