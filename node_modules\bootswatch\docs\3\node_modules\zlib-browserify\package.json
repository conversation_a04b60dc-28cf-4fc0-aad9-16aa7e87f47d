{"_args": [[{"raw": "zlib-browserify@0.0.1", "scope": null, "escapedName": "zlib-browserify", "name": "zlib-browserify", "rawSpec": "0.0.1", "spec": "0.0.1", "type": "version"}, "/Users/<USER>/Development/github/bootswatch/node_modules/grunt-lib-contrib"]], "_from": "zlib-browserify@0.0.1", "_id": "zlib-browserify@0.0.1", "_inCache": true, "_location": "/zlib-browserify", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "_npmVersion": "1.2.2", "_phantomChildren": {}, "_requested": {"raw": "zlib-browserify@0.0.1", "scope": null, "escapedName": "zlib-browserify", "name": "zlib-browserify", "rawSpec": "0.0.1", "spec": "0.0.1", "type": "version"}, "_requiredBy": ["/grunt-lib-contrib"], "_resolved": "https://registry.npmjs.org/zlib-browserify/-/zlib-browserify-0.0.1.tgz", "_shasum": "4fa6a45d00dbc15f318a4afa1d9afc0258e176cc", "_shrinkwrap": null, "_spec": "zlib-browserify@0.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/grunt-lib-contrib", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/brianloveswords/zlib-browserify/issues"}, "dependencies": {}, "description": "Wrapper for zlib.js to allow for browserifyication", "devDependencies": {"tap": "~0.3.3"}, "directories": {"test": "test"}, "dist": {"shasum": "4fa6a45d00dbc15f318a4afa1d9afc0258e176cc", "tarball": "https://registry.npmjs.org/zlib-browserify/-/zlib-browserify-0.0.1.tgz"}, "gitHead": "4be9419f0e8e9dec9629c8a538b33a4efd7df17b", "homepage": "https://github.com/brianloveswords/zlib-browserify#readme", "keywords": ["zlib", "browserify"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "name": "zlib-browserify", "optionalDependencies": {}, "readme": "Zlib in yo' browser.\n", "readmeFilename": "readme.md", "repository": {"type": "git", "url": "git://github.com/brianloveswords/zlib-browserify.git"}, "scripts": {"test": "./node_modules/tap test/*.test.js"}, "version": "0.0.1"}