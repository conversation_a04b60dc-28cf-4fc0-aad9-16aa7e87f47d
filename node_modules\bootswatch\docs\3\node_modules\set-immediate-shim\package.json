{"_args": [[{"raw": "set-immediate-shim@^1.0.0", "scope": null, "escapedName": "set-immediate-shim", "name": "set-immediate-shim", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/each-async"]], "_from": "set-immediate-shim@>=1.0.0 <2.0.0", "_id": "set-immediate-shim@1.0.1", "_inCache": true, "_location": "/set-immediate-shim", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.4.28", "_phantomChildren": {}, "_requested": {"raw": "set-immediate-shim@^1.0.0", "scope": null, "escapedName": "set-immediate-shim", "name": "set-immediate-shim", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/each-async"], "_resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "_shasum": "4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61", "_shrinkwrap": null, "_spec": "set-immediate-shim@^1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/each-async", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/set-immediate-shim/issues"}, "dependencies": {}, "description": "Simple setImmediate shim", "devDependencies": {"ava": "0.0.4", "require-uncached": "^1.0.2"}, "directories": {}, "dist": {"shasum": "4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61", "tarball": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "4c50df7ade5a368e106fee82351ee0a378c990f7", "homepage": "https://github.com/sindresorhus/set-immediate-shim", "keywords": ["setImmediate", "immediate", "setTimeout", "timeout", "shim", "polyfill", "ponyfill"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "set-immediate-shim", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/set-immediate-shim.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}