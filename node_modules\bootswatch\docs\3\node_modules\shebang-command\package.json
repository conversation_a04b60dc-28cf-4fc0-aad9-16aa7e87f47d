{"_args": [[{"raw": "shebang-command@^1.2.0", "scope": null, "escapedName": "shebang-command", "name": "shebang-command", "rawSpec": "^1.2.0", "spec": ">=1.2.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/execa/node_modules/cross-spawn"]], "_from": "shebang-command@>=1.2.0 <2.0.0", "_id": "shebang-command@1.2.0", "_inCache": true, "_location": "/shebang-command", "_nodeVersion": "6.6.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/shebang-command-1.2.0.tgz_1474530105733_0.9689246460329741"}, "_npmUser": {"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}, "_npmVersion": "3.10.6", "_phantomChildren": {}, "_requested": {"raw": "shebang-command@^1.2.0", "scope": null, "escapedName": "shebang-command", "name": "shebang-command", "rawSpec": "^1.2.0", "spec": ">=1.2.0 <2.0.0", "type": "range"}, "_requiredBy": ["/execa/cross-spawn"], "_resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "_shasum": "44aac65b695b03398968c39f363fee5deafdf1ea", "_shrinkwrap": null, "_spec": "shebang-command@^1.2.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/execa/node_modules/cross-spawn", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "dependencies": {"shebang-regex": "^1.0.0"}, "description": "Get the command from a shebang", "devDependencies": {"ava": "*", "xo": "*"}, "directories": {}, "dist": {"shasum": "44aac65b695b03398968c39f363fee5deafdf1ea", "tarball": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "01de9b7d355f21e00417650a6fb1eb56321bc23c", "homepage": "https://github.com/kevva/shebang-command#readme", "keywords": ["cmd", "command", "parse", "shebang"], "license": "MIT", "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "name": "shebang-command", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "scripts": {"test": "xo && ava"}, "version": "1.2.0", "xo": {"ignores": ["test.js"]}}