{"_args": [[{"raw": "tough-cookie@~2.3.0", "scope": null, "escapedName": "tough-cookie", "name": "tough-cookie", "rawSpec": "~2.3.0", "spec": ">=2.3.0 <2.4.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/request"]], "_from": "tough-cookie@>=2.3.0 <2.4.0", "_id": "tough-cookie@2.3.2", "_inCache": true, "_location": "/tough-cookie", "_nodeVersion": "7.0.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tough-cookie-2.3.2.tgz_1477415232912_0.6133609430398792"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "_npmVersion": "3.10.8", "_phantomChildren": {}, "_requested": {"raw": "tough-cookie@~2.3.0", "scope": null, "escapedName": "tough-cookie", "name": "tough-cookie", "rawSpec": "~2.3.0", "spec": ">=2.3.0 <2.4.0", "type": "range"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.2.tgz", "_shasum": "f081f76e4c85720e6c37a5faced737150d84072a", "_shrinkwrap": null, "_spec": "tough-cookie@~2.3.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/request", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {"punycode": "^1.4.1"}, "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "devDependencies": {"async": "^1.4.2", "string.prototype.repeat": "^0.2.0", "vows": "^0.8.1"}, "directories": {}, "dist": {"shasum": "f081f76e4c85720e6c37a5faced737150d84072a", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.2.tgz"}, "engines": {"node": ">=0.8"}, "files": ["lib"], "gitHead": "2610df5dc8ef7373a483d509006e5887572a4076", "homepage": "https://github.com/salesforce/tough-cookie", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/cookie", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "nexxy", "email": "<EMAIL>"}], "name": "tough-cookie", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "scripts": {"suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js", "test": "vows test/*_test.js"}, "version": "2.3.2"}