{"_args": [[{"raw": "string-width@^1.0.1", "scope": null, "escapedName": "string-width", "name": "string-width", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/gauge"]], "_from": "string-width@>=1.0.1 <2.0.0", "_id": "string-width@1.0.2", "_inCache": true, "_location": "/string-width", "_nodeVersion": "4.4.5", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/string-width-1.0.2.tgz_1471188233009_0.6573935742489994"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.15.5", "_phantomChildren": {}, "_requested": {"raw": "string-width@^1.0.1", "scope": null, "escapedName": "string-width", "name": "string-width", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "_requiredBy": ["/cliui", "/gauge", "/wide-align", "/wrap-ansi", "/yargs"], "_resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "_shasum": "118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3", "_shrinkwrap": null, "_spec": "string-width@^1.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/gauge", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "description": "Get the visual width of a string - the number of columns required to display it", "devDependencies": {"ava": "*", "xo": "*"}, "directories": {}, "dist": {"shasum": "118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3", "tarball": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "282cf3d53918a92cc3ee0778dcf938039bcbc47b", "homepage": "https://github.com/sindresorhus/string-width#readme", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "string-width", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}