{"AAInfo": "This is a CodeKit 3 project config file. MODIFYING THE CONTENTS OF THIS FILE IS A POOR LIFE DECISION. Doing so will cause CodeKit to crash and/or corrupt your project. I know it looks like JSON, but it is *not*. Many numbers in this file are 64-bit long long bitFlags, which JSON does not support. These numbers *cannot* be treated as discrete values and if you attempt to parse this file as standard JSON with any public JSON parser, these values will be corrupted. This file is not backwards-compatible with CodeKit 1 or 2. For more information, see https://codekitapp.com/", "buildSteps": [{"name": "Process All Remaining Files and Folders", "stepType": 1, "uuidString": "B8887AE0-A8BF-43F8-BAE4-526AA8E9673F"}], "creatorBuild": "28905", "files": {"/custom.scss": {"aP": 0, "bl": 0, "dP": 10, "dS": 0, "ft": 4, "ma": 0, "oA": 0, "oAP": "/Users/<USER>/Development/github/bootswatch/docs/_assets/css/custom.min.css", "oF": 6, "oS": 3, "uL": 1}}, "hooks": [], "manualImportLinks": {}, "projectAttributes": {"creationDate": 520371309, "displayValue": "scss", "displayValueWasSetByUser": 0, "iconImageName": "meme-obama", "iconImageWasSetByUser": 0}, "projectSettings": {"abortBuildOnError": 1, "alwaysUseExternalServer": 0, "animateCSSInjections": 1, "autoBuildNewItems": 1, "autoprefixerEnableIEGrid": 0, "babel7PresetType": 1, "babelAllowRCFiles": 0, "babelAuxiliaryCommentAfter": "", "babelAuxiliaryCommentBefore": "", "babelConfigType": 0, "babelCustomPluginsList": "", "babelCustomPresetsList": "", "babelInsertModuleIDs": 0, "babelModuleID": "", "babelNoComments": 0, "babelPlugins": {"arrow-functions": {"active": 0}, "async-generator-functions": {"active": 0}, "async-to-generator": {"active": 0}, "block-scoped-functions": {"active": 0}, "block-scoping": {"active": 0}, "class-properties": {"active": 0}, "classes": {"active": 0}, "computed-properties": {"active": 0}, "decorators": {"active": 0}, "destructuring": {"active": 0}, "do-expressions": {"active": 0}, "dotall-regex": {"active": 0}, "duplicate-keys": {"active": 0}, "exponentiation-operator": {"active": 0}, "export-default-from": {"active": 0}, "export-namespace-from": {"active": 0}, "external-helpers": {"active": 0}, "flow-strip-types": {"active": 0}, "for-of": {"active": 0}, "function-bind": {"active": 0}, "function-name": {"active": 0}, "function-sent": {"active": 0}, "instanceof": {"active": 0}, "jscript": {"active": 0}, "literals": {"active": 0}, "logical-assignment-operators": {"active": 0}, "member-expression-literals": {"active": 0}, "merge-sibling-variables": {"active": 0}, "minify-booleans": {"active": 0}, "modules-amd": {"active": 0}, "modules-commonjs": {"active": 0}, "modules-systemjs": {"active": 0}, "modules-umd": {"active": 0}, "new-target": {"active": 0}, "node-env-inline": {"active": 0}, "nullish-coalescing-operator": {"active": 0}, "numeric-separator": {"active": 0}, "object-assign": {"active": 0}, "object-rest-spread": {"active": 0}, "object-set-prototype-of-to-assign": {"active": 0}, "object-super": {"active": 0}, "optional-catch-binding": {"active": 0}, "optional-chaining": {"active": 0}, "parameters": {"active": 0}, "pipeline-operator": {"active": 0}, "property-literals": {"active": 0}, "property-mutators": {"active": 0}, "proto-to-assign": {"active": 0}, "react-constant-elements": {"active": 0}, "react-display-name": {"active": 0}, "react-inline-elements": {"active": 0}, "react-jsx": {"active": 0}, "react-jsx-compat": {"active": 0}, "react-jsx-self": {"active": 0}, "react-jsx-source": {"active": 0}, "regenerator": {"active": 0}, "remove-console": {"active": 0}, "remove-debugger": {"active": 0}, "reserved-words": {"active": 0}, "runtime": {"active": 0}, "shorthand-properties": {"active": 0}, "simplify-comparison-operators": {"active": 0}, "spread": {"active": 0}, "sticky-regex": {"active": 0}, "strict-mode": {"active": 0}, "template-literals": {"active": 0}, "throw-expressions": {"active": 0}, "typeof-symbol": {"active": 0}, "undefined-to-void": {"active": 0}, "unicode-property-regex": {"active": 0}, "unicode-regex": {"active": 0}}, "babelRetainLines": 0, "bowerAbbreviatedPath": "bower_components", "bowerAutoCreateInfoFile": 1, "bowerInstallDevDependencies": 0, "bowerSaveDependencies": 1, "bowerSaveDevDependencies": 0, "bowerUseExactVersion": 0, "browserRefreshDelay": 0, "browserslistString": ">0.2%, last 2 versions, Firefox ESR, not dead", "buildFolderActive": 0, "buildFolderName": "build", "cleanBuild": 1, "coffeeLintFlags2": {"arrow_spacing": {"active": 0, "flagValue": -1}, "camel_case_classes": {"active": 1, "flagValue": -1}, "colon_assignment_spacing": {"active": 0, "flagValue": 1}, "cyclomatic_complexity": {"active": 0, "flagValue": 10}, "duplicate_key": {"active": 1, "flagValue": -1}, "empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "ensure_comprehensions": {"active": 1, "flagValue": -1}, "indentation": {"active": 1, "flagValue": 2}, "line_endings": {"active": 0, "flagValue": 0}, "max_line_length": {"active": 0, "flagValue": 150}, "missing_fat_arrows": {"active": 0, "flagValue": -1}, "newlines_after_classes": {"active": 0, "flagValue": 3}, "no_backticks": {"active": 1, "flagValue": -1}, "no_debugger": {"active": 1, "flagValue": -1}, "no_empty_functions": {"active": 0, "flagValue": -1}, "no_empty_param_list": {"active": 0, "flagValue": -1}, "no_implicit_braces": {"active": 1, "flagValue": -1}, "no_implicit_parens": {"active": 0, "flagValue": -1}, "no_interpolation_in_single_quotes": {"active": 0, "flagValue": -1}, "no_nested_string_interpolation": {"active": 1, "flagValue": -1}, "no_plusplus": {"active": 0, "flagValue": -1}, "no_private_function_fat_arrows": {"active": 1, "flagValue": -1}, "no_stand_alone_at": {"active": 1, "flagValue": -1}, "no_tabs": {"active": 1, "flagValue": -1}, "no_this": {"active": 0, "flagValue": -1}, "no_throwing_strings": {"active": 1, "flagValue": -1}, "no_trailing_semicolons": {"active": 1, "flagValue": -1}, "no_trailing_whitespace": {"active": 1, "flagValue": -1}, "no_unnecessary_double_quotes": {"active": 0, "flagValue": -1}, "no_unnecessary_fat_arrows": {"active": 1, "flagValue": -1}, "non_empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "prefer_english_operator": {"active": 0, "flagValue": -1}, "space_operators": {"active": 0, "flagValue": -1}, "spacing_after_comma": {"active": 1, "flagValue": -1}}, "esLintConfigFileHandlingType": 0, "esLintECMAVersion": 7, "esLintEnvironmentsMask": 1, "esLintRules": {"accessor-pairs": {"active": 0, "optionString": "{'setWithoutGet': true, 'getWithoutSet': false}"}, "array-bracket-newline": {"active": 0, "optionString": "{'multiline': true, 'minItems': null}"}, "array-bracket-spacing": {"active": 0, "optionString": "'never', {'singleValue': false, 'objectsInArrays': false, 'arraysInArrays': false}"}, "array-callback-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "array-element-newline": {"active": 0, "optionString": "'always'"}, "arrow-body-style": {"active": 0, "optionString": "'as-needed', {'requireReturnForObjectLiteral': false}"}, "arrow-parens": {"active": 0, "optionString": "'always'"}, "arrow-spacing": {"active": 0, "optionString": "{'before': true, 'after': true}"}, "block-scoped-var": {"active": 0, "optionString": ""}, "block-spacing": {"active": 0, "optionString": "'always'"}, "brace-style": {"active": 0, "optionString": "'1tbs', {'allowSingleLine': true}"}, "callback-return": {"active": 0, "optionString": "['callback', 'cb', 'next']"}, "camelcase": {"active": 0, "optionString": "{'properties': 'always'}"}, "capitalized-comments": {"active": 0, "optionString": "'always', {'ignoreInlineComments': false, 'ignoreConsecutiveComments': false}"}, "class-methods-use-this": {"active": 0, "optionString": "{'exceptMethods': []}"}, "comma-dangle": {"active": 1, "optionString": "'never'"}, "comma-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "comma-style": {"active": 0, "optionString": "'last'"}, "complexity": {"active": 0, "optionString": "20"}, "computed-property-spacing": {"active": 0, "optionString": "'never'"}, "consistent-return": {"active": 0, "optionString": "{'treatUndefinedAsUnspecified': false}"}, "consistent-this": {"active": 0, "optionString": "'that'"}, "constructor-super": {"active": 1, "optionString": ""}, "curly": {"active": 0, "optionString": "'all'"}, "default-case": {"active": 0, "optionString": ""}, "dot-location": {"active": 0, "optionString": "'object'"}, "dot-notation": {"active": 0, "optionString": "{'allowKeywords': false}"}, "eol-last": {"active": 0, "optionString": "'always'"}, "eqeqeq": {"active": 0, "optionString": "'always', {'null': 'always'}"}, "for-direction": {"active": 0, "optionString": ""}, "func-call-spacing": {"active": 0, "optionString": "'never'"}, "func-name-matching": {"active": 0, "optionString": "'always', {'includeCommonJSModuleExports': false}"}, "func-names": {"active": 0, "optionString": "'always'"}, "func-style": {"active": 0, "optionString": "'expression'"}, "function-paren-newline": {"active": 0, "optionString": "'multiline'"}, "generator-star-spacing": {"active": 0, "optionString": "{'before': true, 'after': false}"}, "getter-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "global-require": {"active": 0, "optionString": ""}, "guard-for-in": {"active": 0, "optionString": ""}, "handle-callback-err": {"active": 0, "optionString": "'err'"}, "id-blacklist": {"active": 0, "optionString": "'data', 'err', 'e', 'cb', 'callback'"}, "id-length": {"active": 0, "optionString": "{'min': 2, 'max': 1000, 'properties': 'always', 'exceptions': ['x', 'i', 'y']}"}, "id-match": {"active": 0, "optionString": "'^[a-z]+([A-Z][a-z]+)*$', {'properties': false, 'onlyDeclarations': true}"}, "implicit-arrow-linebreak": {"active": 0, "optionString": "'beside'"}, "indent": {"active": 0, "optionString": "4, {'SwitchCase': 0, 'VariableDeclarator': 1, 'outerIIFEBody': 1 }"}, "init-declarations": {"active": 0, "optionString": "'always',  {'ignoreForLoopInit': true}"}, "jsx-quotes": {"active": 0, "optionString": "'prefer-double'"}, "key-spacing": {"active": 0, "optionString": "{'singleLine': {'beforeColon': false, 'afterColon': true, 'mode':'strict'}, 'multiLine': {'beforeColon': false, 'afterColon': true, 'align': 'value', 'mode':'minimum'}}"}, "keyword-spacing": {"active": 0, "optionString": "{'before': true, 'after': true, 'overrides': {}}"}, "line-comment-position": {"active": 0, "optionString": "{'position': 'above'}"}, "linebreak-style": {"active": 0, "optionString": "'unix'"}, "lines-around-comment": {"active": 0, "optionString": "{'beforeBlockComment': true}"}, "lines-between-class-members": {"active": 0, "optionString": "'always', {exceptAfterSingleLine: false}"}, "max-classes-per-file": {"active": 0, "optionString": "1"}, "max-depth": {"active": 0, "optionString": "{'max': 4}"}, "max-len": {"active": 0, "optionString": "{'code': 80, 'comments': 80, 'tabWidth': 4, 'ignoreUrls': true, 'ignoreStrings': true, 'ignoreTemplateLiterals': true, 'ignoreRegExpLiterals': true}"}, "max-lines": {"active": 0, "optionString": "{'max': 300, 'skipBlankLines': true, 'skipComments': true}"}, "max-lines-per-function": {"active": 0, "optionString": "{'max': 50, 'skipBlankLines': true, 'skipComments': true, 'IIFEs': false}"}, "max-nested-callbacks": {"active": 0, "optionString": "{'max': 10}"}, "max-params": {"active": 0, "optionString": "{'max': 4}"}, "max-statements": {"active": 0, "optionString": "{'max': 10}, {'ignoreTopLevelFunctions': true}"}, "max-statements-per-line": {"active": 0, "optionString": "{'max': 1}"}, "multiline-comment-style": {"active": 0, "optionString": "'starred-block'"}, "multiline-ternary": {"active": 0, "optionString": "'always'"}, "new-cap": {"active": 0, "optionString": "{'newIsCap': true, 'capIsNew': true, 'newIsCapExceptions': [], 'capIsNewExceptions': ['Array', 'Boolean', 'Date', 'Error', 'Function', 'Number', 'Object', 'RegExp', 'String', 'Symbol'], 'properties': true}"}, "new-parens": {"active": 0, "optionString": ""}, "newline-per-chained-call": {"active": 0, "optionString": "{'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>': 2}"}, "no-alert": {"active": 0, "optionString": ""}, "no-array-constructor": {"active": 0, "optionString": ""}, "no-async-promise-executor": {"active": 0, "optionString": ""}, "no-await-in-loop": {"active": 0, "optionString": ""}, "no-bitwise": {"active": 0, "optionString": "{'allow': ['~'], 'int32Hint': true}"}, "no-buffer-constructor": {"active": 0, "optionString": ""}, "no-caller": {"active": 0, "optionString": ""}, "no-case-declarations": {"active": 1, "optionString": ""}, "no-class-assign": {"active": 1, "optionString": ""}, "no-compare-neg-zero": {"active": 0, "optionString": ""}, "no-cond-assign": {"active": 1, "optionString": "'except-parens'"}, "no-confusing-arrow": {"active": 0, "optionString": "{'allowParens': false}"}, "no-console": {"active": 1, "optionString": "{'allow': ['warn', 'error']}"}, "no-const-assign": {"active": 1, "optionString": ""}, "no-constant-condition": {"active": 1, "optionString": "{'checkLoops': true}"}, "no-continue": {"active": 0, "optionString": ""}, "no-control-regex": {"active": 1, "optionString": ""}, "no-debugger": {"active": 1, "optionString": ""}, "no-delete-var": {"active": 1, "optionString": ""}, "no-div-regex": {"active": 0, "optionString": ""}, "no-dupe-args": {"active": 1, "optionString": ""}, "no-dupe-class-members": {"active": 1, "optionString": ""}, "no-dupe-keys": {"active": 1, "optionString": ""}, "no-duplicate-imports": {"active": 0, "optionString": "{'includeExports': false}"}, "no-else-return": {"active": 0, "optionString": ""}, "no-empty": {"active": 1, "optionString": "{'allowEmptyCatch': false}"}, "no-empty-character-class": {"active": 1, "optionString": ""}, "no-empty-function": {"active": 0, "optionString": "{'allow': []}"}, "no-empty-pattern": {"active": 1, "optionString": ""}, "no-eq-null": {"active": 0, "optionString": ""}, "no-eval": {"active": 0, "optionString": "{'allowIndirect': false}"}, "no-ex-assign": {"active": 1, "optionString": ""}, "no-extend-native": {"active": 0, "optionString": "{'exceptions': []}"}, "no-extra-bind": {"active": 0, "optionString": ""}, "no-extra-boolean-cast": {"active": 1, "optionString": ""}, "no-extra-labels": {"active": 0, "optionString": ""}, "no-extra-parens": {"active": 0, "optionString": "'all', {'conditionalAssign': false, 'returnAssign': false, 'nestedBinaryExpressions': false, 'ignoreJSX': 'none', 'enforceForArrowConditionals': false}"}, "no-extra-semi": {"active": 1, "optionString": ""}, "no-fallthrough": {"active": 1, "optionString": ""}, "no-floating-decimal": {"active": 0, "optionString": ""}, "no-func-assign": {"active": 1, "optionString": ""}, "no-global-assign": {"active": 1, "optionString": "{'exceptions': []}"}, "no-implicit-coercion": {"active": 0, "optionString": "{'boolean': true, 'number': true, 'string': true, 'allow': []}"}, "no-implicit-globals": {"active": 0, "optionString": ""}, "no-implied-eval": {"active": 0, "optionString": ""}, "no-inline-comments": {"active": 0, "optionString": ""}, "no-inner-declarations": {"active": 1, "optionString": "'functions'"}, "no-invalid-regexp": {"active": 1, "optionString": "{'allowConstructorFlags': ['u', 'y']}"}, "no-invalid-this": {"active": 0, "optionString": ""}, "no-irregular-whitespace": {"active": 1, "optionString": "{'skipStrings': true, 'skipComments': false, 'skipRegExps': true, 'skipTemplates': true}"}, "no-iterator": {"active": 0, "optionString": ""}, "no-label-var": {"active": 0, "optionString": ""}, "no-labels": {"active": 0, "optionString": "{'allowLoop': false, 'allowSwitch': false}"}, "no-lone-blocks": {"active": 0, "optionString": ""}, "no-lonely-if": {"active": 0, "optionString": ""}, "no-loop-func": {"active": 0, "optionString": ""}, "no-magic-numbers": {"active": 0, "optionString": "{'ignore': [], 'ignoreArrayIndexes': true, 'enforceConst': false, 'detectObjects': false}"}, "no-misleading-character-class": {"active": 0, "optionString": ""}, "no-mixed-operators": {"active": 0, "optionString": "{'groups': [['+', '-', '*', '/', '%', '**'], ['&', '|', '^', '~', '<<', '>>', '>>>'], ['==', '!=', '===', '!==', '>', '>=', '<', '<='], ['&&', '||'], ['in', 'instanceof']], 'allowSamePrecedence': true}"}, "no-mixed-requires": {"active": 0, "optionString": "{'grouping': false, 'allowCall': false }"}, "no-mixed-spaces-and-tabs": {"active": 0, "optionString": ""}, "no-multi-assign": {"active": 0, "optionString": ""}, "no-multi-spaces": {"active": 0, "optionString": "{'exceptions': {'Property': true, 'BinaryExpression': false, 'VariableDeclarator': false, 'ImportDeclaration': false}}"}, "no-multi-str": {"active": 0, "optionString": ""}, "no-multiple-empty-lines": {"active": 0, "optionString": "{'max': 2, 'maxBOF': 2, 'maxEOF': 2}"}, "no-negated-condition": {"active": 0, "optionString": ""}, "no-nested-ternary": {"active": 0, "optionString": ""}, "no-new": {"active": 0, "optionString": ""}, "no-new-func": {"active": 0, "optionString": ""}, "no-new-object": {"active": 0, "optionString": ""}, "no-new-require": {"active": 0, "optionString": ""}, "no-new-symbol": {"active": 1, "optionString": ""}, "no-new-wrappers": {"active": 0, "optionString": ""}, "no-obj-calls": {"active": 1, "optionString": ""}, "no-octal": {"active": 1, "optionString": ""}, "no-octal-escape": {"active": 0, "optionString": ""}, "no-param-reassign": {"active": 0, "optionString": "{'props': false}"}, "no-path-concat": {"active": 0, "optionString": ""}, "no-plusplus": {"active": 0, "optionString": "{'allowForLoopAfterthoughts': false}"}, "no-process-env": {"active": 0, "optionString": ""}, "no-process-exit": {"active": 0, "optionString": ""}, "no-proto": {"active": 0, "optionString": ""}, "no-prototype-builtins": {"active": 0, "optionString": ""}, "no-redeclare": {"active": 1, "optionString": "{'builtinGlobals': false}"}, "no-regex-spaces": {"active": 1, "optionString": ""}, "no-restricted-globals": {"active": 0, "optionString": "'event', 'fdescribe'"}, "no-restricted-imports": {"active": 0, "optionString": ""}, "no-restricted-modules": {"active": 0, "optionString": ""}, "no-restricted-properties": {"active": 0, "optionString": "[{'object': 'disallowedObjectName', 'property': 'disallowedPropertyName'}, {'object': 'disallowedObjectName', 'property': 'anotherDisallowedPropertyName', 'message': 'Please use allowedObjectName.allowedPropertyName.'}]"}, "no-restricted-syntax": {"active": 0, "optionString": "'FunctionExpression', 'WithStatement'"}, "no-return-assign": {"active": 0, "optionString": "'except-parens'"}, "no-return-await": {"active": 0, "optionString": ""}, "no-script-url": {"active": 0, "optionString": ""}, "no-self-assign": {"active": 1, "optionString": "{'props': false}"}, "no-self-compare": {"active": 0, "optionString": ""}, "no-sequences": {"active": 0, "optionString": ""}, "no-shadow": {"active": 0, "optionString": "{'builtinGlobals': false, 'hoist': 'functions', 'allow': []}"}, "no-shadow-restricted-names": {"active": 0, "optionString": ""}, "no-sparse-arrays": {"active": 1, "optionString": ""}, "no-sync": {"active": 0, "optionString": "{'allowAtRootLevel': false}"}, "no-tabs": {"active": 0, "optionString": ""}, "no-template-curly-in-string": {"active": 0, "optionString": ""}, "no-ternary": {"active": 0, "optionString": ""}, "no-this-before-super": {"active": 1, "optionString": ""}, "no-throw-literal": {"active": 0, "optionString": ""}, "no-trailing-spaces": {"active": 0, "optionString": "{'skipBlankLines': false, 'ignoreComments': false}"}, "no-undef": {"active": 1, "optionString": "{'typeof': false}"}, "no-undef-init": {"active": 0, "optionString": ""}, "no-undefined": {"active": 0, "optionString": ""}, "no-underscore-dangle": {"active": 0, "optionString": "{'allow': [], 'allowAfterThis': false, 'allowAfterSuper': false, 'enforceInMethodNames': false}"}, "no-unexpected-multiline": {"active": 1, "optionString": ""}, "no-unmodified-loop-condition": {"active": 0, "optionString": ""}, "no-unneeded-ternary": {"active": 0, "optionString": "{'defaultAssignment': true}"}, "no-unreachable": {"active": 1, "optionString": ""}, "no-unsafe-finally": {"active": 1, "optionString": ""}, "no-unsafe-negation": {"active": 1, "optionString": ""}, "no-unused-expressions": {"active": 0, "optionString": "{'allowShortCircuit': false, 'allowTernary': false, 'allowTaggedTemplates': false}"}, "no-unused-labels": {"active": 1, "optionString": ""}, "no-unused-vars": {"active": 1, "optionString": "{'vars': 'all', 'args': 'after-used', 'caughtErrors': 'none', 'ignoreRestSiblings': false}"}, "no-use-before-define": {"active": 0, "optionString": "{'functions': true, 'classes': true, 'variables': true}"}, "no-useless-call": {"active": 0, "optionString": ""}, "no-useless-computed-key": {"active": 0, "optionString": ""}, "no-useless-concat": {"active": 0, "optionString": ""}, "no-useless-constructor": {"active": 0, "optionString": ""}, "no-useless-escape": {"active": 0, "optionString": ""}, "no-useless-rename": {"active": 0, "optionString": "{'ignoreDestructuring': false, 'ignoreImport': false, 'ignoreExport': false}"}, "no-useless-return": {"active": 0, "optionString": ""}, "no-var": {"active": 0, "optionString": ""}, "no-void": {"active": 0, "optionString": ""}, "no-warning-comments": {"active": 0, "optionString": "{'terms': ['todo', 'fixme', 'xxx'], 'location': 'start'}"}, "no-whitespace-before-property": {"active": 0, "optionString": ""}, "no-with": {"active": 0, "optionString": ""}, "nonblock-statement-body-position": {"active": 0, "optionString": "'beside'"}, "object-curly-newline": {"active": 0, "optionString": "{'ObjectExpression': {'multiline': true}, 'ObjectPattern': {'multiline': true}}"}, "object-curly-spacing": {"active": 0, "optionString": "'never'"}, "object-property-newline": {"active": 0, "optionString": "{'allowAllPropertiesOnSameLine': true}"}, "object-shorthand": {"active": 0, "optionString": "'always', {'avoidQuotes': false, 'ignoreConstructors': false}"}, "one-var": {"active": 0, "optionString": "'always'"}, "one-var-declaration-per-line": {"active": 0, "optionString": "'always'"}, "operator-assignment": {"active": 0, "optionString": "'always'"}, "operator-linebreak": {"active": 0, "optionString": "'after', {'overrides': {'?': 'after', '+=': 'none'}}"}, "padded-blocks": {"active": 0, "optionString": "{'blocks': 'always', 'switches': 'always', 'classes': 'always'}"}, "padding-line-between-statements": {"active": 0, "optionString": "{blankLine: 'always', prev:'*', next:'return'}"}, "prefer-arrow-callback": {"active": 0, "optionString": ""}, "prefer-const": {"active": 0, "optionString": "{'destructuring': 'any', 'ignoreReadBeforeAssign': false}"}, "prefer-destructuring": {"active": 0, "optionString": "{'array': true, 'object': true}, {'enforceForRenamedProperties': false}"}, "prefer-numeric-literals": {"active": 0, "optionString": ""}, "prefer-object-spread": {"active": 0, "optionString": ""}, "prefer-promise-reject-errors": {"active": 0, "optionString": "{'allowEmptyReject': false}"}, "prefer-rest-params": {"active": 0, "optionString": ""}, "prefer-spread": {"active": 0, "optionString": ""}, "prefer-template": {"active": 0, "optionString": ""}, "quote-props": {"active": 0, "optionString": "'always'"}, "quotes": {"active": 0, "optionString": "'double', {'avoidEscape': true, 'allowTemplateLiterals': true}"}, "radix": {"active": 0, "optionString": "'always'"}, "require-atomic-updates": {"active": 0, "optionString": ""}, "require-await": {"active": 0, "optionString": ""}, "require-jsdoc": {"active": 0, "optionString": "{'require': {'FunctionDeclaration': true, 'MethodDefinition': false, 'ClassDeclaration': false, 'ArrowFunctionExpression': false}}"}, "require-unicode-regexp": {"active": 0, "optionString": ""}, "require-yield": {"active": 1, "optionString": ""}, "rest-spread-spacing": {"active": 0, "optionString": "'never'"}, "semi": {"active": 0, "optionString": "'always', {'omitLastInOneLineBlock': false}"}, "semi-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "semi-style": {"active": 0, "optionString": "'last'"}, "sort-imports": {"active": 0, "optionString": "{'ignoreCase': false, 'ignoreMemberSort': true, 'memberSyntaxSortOrder': ['none', 'all', 'multiple', 'single']}"}, "sort-keys": {"active": 0, "optionString": "'asc', {'caseSensitive': true, 'natural': false}"}, "sort-vars": {"active": 0, "optionString": "{'ignoreCase': false}"}, "space-before-blocks": {"active": 0, "optionString": "{'functions': 'always', 'keywords': 'always', 'classes': 'always'}"}, "space-before-function-paren": {"active": 0, "optionString": "{'anonymous': 'always', 'named': 'never'}"}, "space-in-parens": {"active": 0, "optionString": "'never', {'exceptions': []}"}, "space-infix-ops": {"active": 0, "optionString": "{'int32Hint': false}"}, "space-unary-ops": {"active": 0, "optionString": "{'words': true, 'nonwords': false, 'overrides': {}}"}, "spaced-comment": {"active": 0, "optionString": "'always', {'line': {'markers': ['/'], 'exceptions': ['-', '+']}, 'block': {'markers': ['!'], 'exceptions': ['*'], 'balanced': false}}"}, "strict": {"active": 0, "optionString": "'safe'"}, "switch-colon-spacing": {"active": 0, "optionString": "{'after': true, 'before': false}"}, "symbol-description": {"active": 0, "optionString": ""}, "template-curly-spacing": {"active": 0, "optionString": "'never'"}, "template-tag-spacing": {"active": 0, "optionString": "'never'"}, "unicode-bom": {"active": 0, "optionString": "'never'"}, "use-isnan": {"active": 1, "optionString": ""}, "valid-jsdoc": {"active": 0, "optionString": "{'prefer': {'return': 'returns'}, 'requireReturn': true, 'requireReturnDescription': true, 'requireReturnType': true, 'requireParamDescription': true}"}, "valid-typeof": {"active": 1, "optionString": "{'requireStringLiterals': true}"}, "vars-on-top": {"active": 0, "optionString": ""}, "wrap-iife": {"active": 0, "optionString": "'outside'"}, "wrap-regex": {"active": 0, "optionString": ""}, "yield-star-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "yoda": {"active": 0, "optionString": "'never', {'except<PERSON><PERSON><PERSON>': false, 'onlyEquality': false}"}}, "esLintSourceType": 0, "externalServerAddress": "http://localhost:8888", "gitIgnoreBuildFolder": 1, "hideConfigFile": 0, "jsCheckerReservedNamesString": "", "jsHintFlags2": {"asi": {"active": 0, "flagValue": -1}, "bitwise": {"active": 1, "flagValue": -1}, "boss": {"active": 0, "flagValue": -1}, "browser": {"active": 1, "flagValue": -1}, "browserify": {"active": 0, "flagValue": -1}, "camelcase": {"active": 0, "flagValue": -1}, "couch": {"active": 0, "flagValue": -1}, "curly": {"active": 1, "flagValue": -1}, "debug": {"active": 0, "flagValue": -1}, "devel": {"active": 0, "flagValue": -1}, "dojo": {"active": 0, "flagValue": -1}, "elision": {"active": 1, "flagValue": -1}, "eqeqeq": {"active": 1, "flagValue": -1}, "eqnull": {"active": 0, "flagValue": -1}, "es3": {"active": 0, "flagValue": -1}, "esnext": {"active": 0, "flagValue": -1}, "evil": {"active": 0, "flagValue": -1}, "expr": {"active": 0, "flagValue": -1}, "forin": {"active": 0, "flagValue": -1}, "freeze": {"active": 1, "flagValue": -1}, "funcscope": {"active": 0, "flagValue": -1}, "futurehostile": {"active": 0, "flagValue": -1}, "globalstrict": {"active": 0, "flagValue": -1}, "immed": {"active": 0, "flagValue": -1}, "indent": {"active": 0, "flagValue": 4}, "iterator": {"active": 0, "flagValue": -1}, "jasmine": {"active": 0, "flagValue": -1}, "jquery": {"active": 1, "flagValue": -1}, "lastsemic": {"active": 0, "flagValue": -1}, "latedef": {"active": 1, "flagValue": -1}, "laxbreak": {"active": 0, "flagValue": -1}, "laxcomma": {"active": 0, "flagValue": -1}, "loopfunc": {"active": 0, "flagValue": -1}, "maxcomplexity": {"active": 0, "flagValue": 10}, "maxdepth": {"active": 0, "flagValue": 3}, "maxlen": {"active": 0, "flagValue": 150}, "maxparams": {"active": 0, "flagValue": 3}, "maxstatements": {"active": 0, "flagValue": 4}, "mocha": {"active": 0, "flagValue": -1}, "mootools": {"active": 0, "flagValue": -1}, "moz": {"active": 0, "flagValue": -1}, "multistr": {"active": 0, "flagValue": -1}, "newcap": {"active": 1, "flagValue": -1}, "noarg": {"active": 1, "flagValue": -1}, "nocomma": {"active": 0, "flagValue": -1}, "node": {"active": 0, "flagValue": -1}, "noempty": {"active": 0, "flagValue": -1}, "nonbsp": {"active": 0, "flagValue": -1}, "nonew": {"active": 1, "flagValue": -1}, "nonstandard": {"active": 0, "flagValue": -1}, "notypeof": {"active": 1, "flagValue": -1}, "noyield": {"active": 0, "flagValue": -1}, "onecase": {"active": 0, "flagValue": -1}, "phantom": {"active": 0, "flagValue": -1}, "plusplus": {"active": 0, "flagValue": -1}, "proto": {"active": 0, "flagValue": -1}, "prototypejs": {"active": 0, "flagValue": -1}, "qunit": {"active": 0, "flagValue": -1}, "regexp": {"active": 1, "flagValue": -1}, "rhino": {"active": 0, "flagValue": -1}, "scripturl": {"active": 0, "flagValue": -1}, "shadow": {"active": 0, "flagValue": -1}, "shelljs": {"active": 0, "flagValue": -1}, "singleGroups": {"active": 0, "flagValue": -1}, "strict": {"active": 0, "flagValue": -1}, "sub": {"active": 0, "flagValue": -1}, "supernew": {"active": 0, "flagValue": -1}, "typed": {"active": 0, "flagValue": -1}, "undef": {"active": 1, "flagValue": -1}, "unused": {"active": 1, "flagValue": -1}, "varstmt": {"active": 0, "flagValue": -1}, "withstmt": {"active": 0, "flagValue": -1}, "worker": {"active": 0, "flagValue": -1}, "wsh": {"active": 0, "flagValue": -1}, "yui": {"active": 0, "flagValue": -1}}, "jsLintFlags2": {"bitwise": {"active": 0, "flagValue": -1}, "browser": {"active": 1, "flagValue": -1}, "couch": {"active": 0, "flagValue": -1}, "devel": {"active": 0, "flagValue": -1}, "es6": {"active": 0, "flagValue": -1}, "eval": {"active": 0, "flagValue": -1}, "for": {"active": 0, "flagValue": -1}, "maxlen": {"active": 0, "flagValue": 150}, "node": {"active": 0, "flagValue": -1}, "this": {"active": 0, "flagValue": -1}, "white": {"active": 0, "flagValue": -1}}, "languageDefaultsCOFFEE": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createSourceMap": 0, "minifyOutput": 1, "outputStyle": 0, "syntaxCheckerStyle": 1, "transpilerStyle": 1}, "languageDefaultsCSS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.css", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createSourceMap": 0, "outputStyle": 3, "shouldRunAutoprefixer": 1, "shouldRunBless": 0}, "languageDefaultsGIF": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.gif", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0}, "languageDefaultsHAML": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "escapeHTMLCharacters": 0, "noEscapeInAttributes": 0, "outputFormat": 2, "shouldRunCacheBuster": 0, "useCDATA": 0, "useDoubleQuotes": 0, "useUnixNewlines": 0}, "languageDefaultsJPG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.jpg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "outputFormat": 0, "quality": 100}, "languageDefaultsJS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.js", "autoOutputPathRelativePath": "/min", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createSourceMap": 0, "minifyOutput": 1, "syntaxCheckerStyle": 1, "transpilerStyle": 0}, "languageDefaultsJSON": {"autoOutputAction": 1, "autoOutputPathFilenamePattern": "*-min.json", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "orderOutput": 0, "outputStyle": 1}, "languageDefaultsKIT": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "kit", "autoOutputPathReplace2": "html", "autoOutputPathStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsLESS": {"allowInsecureImports": 0, "autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "less", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "createSourceMap": 1, "enableJavascript": 0, "ieCompatibility": 1, "mathStyle": 0, "outputStyle": 0, "rewriteURLStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "strictImports": 0, "strictUnits": 0}, "languageDefaultsMARKDOWN": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "criticStyle": 0, "enableFootnotes": 1, "enableLabels": 1, "enableSmartQuotes": 1, "maskEmailAddresses": 1, "outputFormat": 0, "outputStyle": 0, "parseMetadata": 1, "processHTML": 0, "randomFootnoteNumbers": 0, "shouldRunCacheBuster": 0, "useCompatibilityMode": 0}, "languageDefaultsOTHER": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.*", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsPNG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.png", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "optimizerType": 1, "quality": 100}, "languageDefaultsPUG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileDebug": 1, "outputStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSASS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "sass", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "createSourceMap": 0, "debugStyle": 0, "decimalPrecision": 10, "outputStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "useLibsass": 1}, "languageDefaultsSLIM": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileOnly": 0, "logicless": 0, "outputFormat": 0, "outputStyle": 1, "railsCompatible": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSTYLUS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "stylus", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "createSourceMap": 0, "debugStyle": 0, "importCSS": 0, "outputStyle": 0, "resolveRelativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0}, "languageDefaultsSVG": {"autoOutputAction": 2, "autoOutputPathFilenamePattern": "*.svg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "pluginMask": 3758088159}, "languageDefaultsTS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "/js", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createDeclarationFile": 0, "createSourceMap": 0, "minifyOutput": 0, "moduleResolutionType": 0, "moduleType": 2, "removeComments": 0, "targetECMAVersion": 0}, "languageDefaultsUserDefined": [], "overrideExternalServerCSS": 0, "previewPathAddition": "", "skippedFoldersString": "log, _logs, logs, _cache, cache, .idea, /storage/framework/sessions, node_modules", "sourceFolderName": "source", "susyVersion": 3, "tsAllowSyntheticDefaultImports": 0, "tsAllowUnreachableCode": 0, "tsAllowUnusedLabels": 0, "tsAlwaysStrict": 0, "tsDownlevelIteration": 0, "tsEmitBOM": 0, "tsEmitDecoratorMetadata": 0, "tsESModuleInterop": 0, "tsForceConsistentCasingInFileNames": 0, "tsImportHelpers": 0, "tsIsolatedModules": 0, "tsJSXFactory": "React.createElement", "tsKeyofStringsOnly": 0, "tsNoEmitHelpers": 0, "tsNoFallthroughCasesInSwitch": 0, "tsNoImplicitAny": 0, "tsNoImplicitReturns": 0, "tsNoImplicitThis": 0, "tsNoImplicitUseStrict": 0, "tsNoLib": 0, "tsNoResolve": 0, "tsNoStrictGenericChecks": 0, "tsNoUnusedLocals": 0, "tsNoUnusedParameters": 0, "tsPreserveConstEnums": 0, "tsPreserveSymlinks": 0, "tsResolveJsonModule": 0, "tsSkipLibCheck": 0, "tsStrictFunctionTypes": 0, "tsStrictNullChecks": 0, "tsStrictPropertyInitialization": 0, "tsStripInternal": 0, "tsSuppressExcessPropertyErrors": 0, "tsSuppressImplicitAnyIndexErrors": 0, "uglifyDefinesString": "", "uglifyFlags2": {"arguments": {"active": 1, "flagValue": -1}, "ascii_only": {"active": 0, "flagValue": -1}, "bare_returns": {"active": 0, "flagValue": -1}, "booleans": {"active": 1, "flagValue": -1}, "braces": {"active": 0, "flagValue": -1}, "collapse_vars": {"active": 1, "flagValue": -1}, "comments": {"active": 1, "flagValue": -1}, "comparisons": {"active": 1, "flagValue": -1}, "compress": {"active": 1, "flagValue": -1}, "conditionals": {"active": 1, "flagValue": -1}, "dead_code": {"active": 0, "flagValue": -1}, "directives": {"active": 1, "flagValue": -1}, "drop_console": {"active": 0, "flagValue": -1}, "drop_debugger": {"active": 1, "flagValue": -1}, "eval": {"active": 0, "flagValue": -1}, "evaluate": {"active": 1, "flagValue": -1}, "expression": {"active": 0, "flagValue": -1}, "hoist_funs": {"active": 1, "flagValue": -1}, "hoist_props": {"active": 1, "flagValue": -1}, "hoist_vars": {"active": 0, "flagValue": -1}, "html5_comments": {"active": 1, "flagValue": -1}, "ie8": {"active": 0, "flagValue": -1}, "if_return": {"active": 1, "flagValue": -1}, "indent-start": {"active": 0, "flagValue": 0}, "indent_level": {"active": 0, "flagValue": 4}, "inline": {"active": 1, "flagValue": 3}, "inline_script": {"active": 1, "flagValue": -1}, "join_vars": {"active": 1, "flagValue": -1}, "keep_fargs": {"active": 0, "flagValue": -1}, "keep_fnames": {"active": 0, "flagValue": -1}, "keep_infinity": {"active": 0, "flagValue": -1}, "keep_quoted_props": {"active": 0, "flagValue": -1}, "loops": {"active": 1, "flagValue": -1}, "mangle": {"active": 1, "flagValue": -1}, "max_line_len": {"active": 1, "flagValue": 32000}, "negate_iife": {"active": 1, "flagValue": -1}, "passes": {"active": 1, "flagValue": 1}, "preserve_line": {"active": 0, "flagValue": -1}, "properties": {"active": 1, "flagValue": -1}, "pure_getters": {"active": 0, "flagValue": -1}, "quote_keys": {"active": 0, "flagValue": -1}, "quote_style": {"active": 1, "flagValue": 0}, "reduce_funcs": {"active": 1, "flagValue": -1}, "reduce_vars": {"active": 1, "flagValue": -1}, "semicolons": {"active": 1, "flagValue": -1}, "sequences": {"active": 1, "flagValue": -1}, "shebang": {"active": 1, "flagValue": -1}, "side_effects": {"active": 1, "flagValue": -1}, "switches": {"active": 1, "flagValue": -1}, "toplevel": {"active": 0, "flagValue": -1}, "typeofs": {"active": 1, "flagValue": -1}, "unsafe": {"active": 0, "flagValue": -1}, "unsafe_comps": {"active": 0, "flagValue": -1}, "unsafe_Function": {"active": 0, "flagValue": -1}, "unsafe_math": {"active": 0, "flagValue": -1}, "unsafe_proto": {"active": 0, "flagValue": -1}, "unsafe_regexp": {"active": 0, "flagValue": -1}, "unsafe_undefined": {"active": 0, "flagValue": -1}, "unused": {"active": 0, "flagValue": -1}, "warnings": {"active": 0, "flagValue": -1}, "webkit": {"active": 0, "flagValue": -1}, "width": {"active": 1, "flagValue": 80}, "wrap_iife": {"active": 0, "flagValue": -1}}, "uglifyReservedNamesString": "$", "websiteRelativeRoot": ""}, "settingsFileVersion": "3"}