{"_args": [[{"raw": "split@0.3", "scope": null, "escapedName": "split", "name": "split", "rawSpec": "0.3", "spec": ">=0.3.0 <0.4.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream"]], "_from": "split@>=0.3.0 <0.4.0", "_id": "split@0.3.3", "_inCache": true, "_location": "/split", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_npmVersion": "2.2.0", "_phantomChildren": {}, "_requested": {"raw": "split@0.3", "scope": null, "escapedName": "split", "name": "split", "rawSpec": "0.3", "spec": ">=0.3.0 <0.4.0", "type": "range"}, "_requiredBy": ["/event-stream"], "_resolved": "https://registry.npmjs.org/split/-/split-0.3.3.tgz", "_shasum": "cd0eea5e63a211dfff7eb0f091c4133e2d0dd28f", "_shrinkwrap": null, "_spec": "split@0.3", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bit.ly/dominictarr"}, "bugs": {"url": "https://github.com/dominictarr/split/issues"}, "dependencies": {"through": "2"}, "description": "split a Text Stream into a Line Stream", "devDependencies": {"asynct": "*", "event-stream": "~3.0.2", "it-is": "1", "stream-spec": "~0.2", "string-to-stream": "~1.0.0", "ubelt": "~2.9"}, "directories": {}, "dist": {"shasum": "cd0eea5e63a211dfff7eb0f091c4133e2d0dd28f", "tarball": "https://registry.npmjs.org/split/-/split-0.3.3.tgz"}, "engines": {"node": "*"}, "gitHead": "3fe392ef2aabce9db7abe8185ec48389ae224883", "homepage": "http://github.com/dominictarr/split", "license": "MIT", "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "name": "split", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/dominictarr/split.git"}, "scripts": {"test": "asynct test/"}, "version": "0.3.3"}