{"_args": [[{"raw": "source-map@~0.1.40", "scope": null, "escapedName": "source-map", "name": "source-map", "rawSpec": "~0.1.40", "spec": ">=0.1.40 <0.2.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/postcss"]], "_from": "source-map@>=0.1.40 <0.2.0", "_id": "source-map@0.1.43", "_inCache": true, "_location": "/source-map", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.4.9", "_phantomChildren": {}, "_requested": {"raw": "source-map@~0.1.40", "scope": null, "escapedName": "source-map", "name": "source-map", "rawSpec": "~0.1.40", "spec": ">=0.1.40 <0.2.0", "type": "range"}, "_requiredBy": ["/postcss"], "_resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "_shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "_shrinkwrap": null, "_spec": "source-map@~0.1.40", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/postcss", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"amdefine": ">=0.0.4"}, "description": "Generates and consumes source maps", "devDependencies": {"dryice": ">=0.4.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz"}, "engines": {"node": ">=0.8.0"}, "homepage": "https://github.com/mozilla/source-map", "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "main": "./lib/source-map.js", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "source-map", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "node Makefile.dryice.js", "test": "node test/run-tests.js"}, "version": "0.1.43"}