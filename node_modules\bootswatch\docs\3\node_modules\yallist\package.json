{"_args": [[{"raw": "yallist@^2.1.2", "scope": null, "escapedName": "yallist", "name": "yallist", "rawSpec": "^2.1.2", "spec": ">=2.1.2 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass/node_modules/lru-cache"]], "_from": "yallist@>=2.1.2 <3.0.0", "_id": "yallist@2.1.2", "_inCache": true, "_location": "/yallist", "_nodeVersion": "8.0.0-pre", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.2.tgz_1489443365033_0.47744474792853"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "4.3.0", "_phantomChildren": {}, "_requested": {"raw": "yallist@^2.1.2", "scope": null, "escapedName": "yallist", "name": "yallist", "rawSpec": "^2.1.2", "spec": ">=2.1.2 <3.0.0", "type": "range"}, "_requiredBy": ["/node-sass/lru-cache"], "_resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "_shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "_shrinkwrap": null, "_spec": "yallist@^2.1.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass/node_modules/lru-cache", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "dependencies": {}, "description": "Yet Another Linked List", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"}, "files": ["yallist.js", "iterator.js"], "gitHead": "566cd4cd1e2ce57ffa84e295981cd9aa72319391", "homepage": "https://github.com/isaacs/yallist#readme", "license": "ISC", "main": "yallist.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "yallist", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "2.1.2"}