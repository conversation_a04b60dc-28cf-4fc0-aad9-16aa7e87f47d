{"_args": [[{"raw": "validate-npm-package-license@^3.0.1", "scope": null, "escapedName": "validate-npm-package-license", "name": "validate-npm-package-license", "rawSpec": "^3.0.1", "spec": ">=3.0.1 <4.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-package-data"]], "_from": "validate-npm-package-license@>=3.0.1 <4.0.0", "_id": "validate-npm-package-license@3.0.1", "_inCache": true, "_location": "/validate-npm-package-license", "_nodeVersion": "0.12.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.13.5", "_phantomChildren": {}, "_requested": {"raw": "validate-npm-package-license@^3.0.1", "scope": null, "escapedName": "validate-npm-package-license", "name": "validate-npm-package-license", "rawSpec": "^3.0.1", "spec": ">=3.0.1 <4.0.0", "type": "range"}, "_requiredBy": ["/normalize-package-data"], "_resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz", "_shasum": "2804babe712ad3379459acfbe24746ab2c303fbc", "_shrinkwrap": null, "_spec": "validate-npm-package-license@^3.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-package-data", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, "bugs": {"url": "https://github.com/kemitchell/validate-npm-package-license.js/issues"}, "dependencies": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}, "description": "Give me a string and I'll tell you if it's a valid npm package license string", "devDependencies": {"defence-cli": "^1.0.1", "replace-require-self": "^1.0.0"}, "directories": {}, "dist": {"shasum": "2804babe712ad3379459acfbe24746ab2c303fbc", "tarball": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz"}, "gitHead": "00200d28f9960985f221bc1a8a71e4760daf39bf", "homepage": "https://github.com/kemitchell/validate-npm-package-license.js#readme", "keywords": ["license", "npm", "package", "validation"], "license": "Apache-2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "name": "validate-npm-package-license", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/validate-npm-package-license.js.git"}, "scripts": {"test": "defence README.md | replace-require-self | node"}, "version": "3.0.1"}