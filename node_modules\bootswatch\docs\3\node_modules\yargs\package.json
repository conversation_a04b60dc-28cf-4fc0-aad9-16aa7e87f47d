{"_args": [[{"raw": "yargs@^7.0.0", "scope": null, "escapedName": "yargs", "name": "yargs", "rawSpec": "^7.0.0", "spec": ">=7.0.0 <8.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/sass-graph"]], "_from": "yargs@>=7.0.0 <8.0.0", "_id": "yargs@7.1.0", "_inCache": true, "_location": "/yargs", "_nodeVersion": "6.9.5", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yargs-7.1.0.tgz_1492119927787_0.18849953636527061"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "4.5.0", "_phantomChildren": {}, "_requested": {"raw": "yargs@^7.0.0", "scope": null, "escapedName": "yargs", "name": "yargs", "rawSpec": "^7.0.0", "spec": ">=7.0.0 <8.0.0", "type": "range"}, "_requiredBy": ["/sass-graph"], "_resolved": "https://registry.npmjs.org/yargs/-/yargs-7.1.0.tgz", "_shasum": "6ba318eb16961727f5d284f8ea003e8d6154d0c8", "_shrinkwrap": null, "_spec": "yargs@^7.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/sass-graph", "bugs": {"url": "https://github.com/yargs/yargs/issues"}, "dependencies": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.0"}, "description": "yargs the modern, pirate-themed, successor to optimist.", "devDependencies": {"chai": "^3.4.1", "chalk": "^1.1.3", "coveralls": "^2.11.11", "cpr": "^2.0.0", "cross-spawn": "^5.0.1", "es6-promise": "^4.0.2", "hashish": "0.0.4", "mocha": "^3.0.1", "nyc": "^10.0.0", "rimraf": "^2.5.0", "standard": "^8.6.0", "standard-version": "^3.0.0", "which": "^1.2.9"}, "directories": {}, "dist": {"shasum": "6ba318eb16961727f5d284f8ea003e8d6154d0c8", "tarball": "https://registry.npmjs.org/yargs/-/yargs-7.1.0.tgz"}, "engine": {"node": ">=0.10"}, "files": ["index.js", "yargs.js", "lib", "locales", "completion.sh.hbs", "LICENSE"], "gitHead": "e7359d632595c3a5fcfd691994859b66e8943c85", "greenkeeper": {"ignore": ["string-width", "read-pkg-up", "camelcase"]}, "homepage": "http://yargs.js.org/", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "main": "./index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "chevex", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "name": "yargs", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/yargs/yargs.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc --cache mocha --require ./test/before.js --timeout=8000 --check-leaks"}, "standard": {"ignore": ["**/example/**"]}, "version": "7.1.0"}