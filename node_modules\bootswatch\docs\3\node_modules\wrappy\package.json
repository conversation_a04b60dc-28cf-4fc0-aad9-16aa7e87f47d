{"_args": [[{"raw": "wrappy@1", "scope": null, "escapedName": "wrappy", "name": "wrappy", "rawSpec": "1", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/inflight"]], "_from": "wrappy@>=1.0.0 <2.0.0", "_id": "wrappy@1.0.2", "_inCache": true, "_location": "/wrappy", "_nodeVersion": "5.10.1", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/wrappy-1.0.2.tgz_1463527848281_0.037129373755306005"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "_npmVersion": "3.9.1", "_phantomChildren": {}, "_requested": {"raw": "wrappy@1", "scope": null, "escapedName": "wrappy", "name": "wrappy", "rawSpec": "1", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/inflight", "/once"], "_resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "_shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "_shrinkwrap": null, "_spec": "wrappy@1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/inflight", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "dependencies": {}, "description": "Callback wrapping utility", "devDependencies": {"tap": "^2.3.1"}, "directories": {"test": "test"}, "dist": {"shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "tarball": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"}, "files": ["wrappy.js"], "gitHead": "71d91b6dc5bdeac37e218c2cf03f9ab55b60d214", "homepage": "https://github.com/npm/wrappy", "license": "ISC", "main": "wrappy.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "name": "wrappy", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "scripts": {"test": "tap --coverage test/*.js"}, "version": "1.0.2"}