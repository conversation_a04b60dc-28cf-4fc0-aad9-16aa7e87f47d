{"_args": [[{"raw": "tunnel-agent@~0.4.1", "scope": null, "escapedName": "tunnel-agent", "name": "tunnel-agent", "rawSpec": "~0.4.1", "spec": ">=0.4.1 <0.5.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/request"]], "_from": "tunnel-agent@>=0.4.1 <0.5.0", "_id": "tunnel-agent@0.4.3", "_inCache": true, "_location": "/tunnel-agent", "_nodeVersion": "5.9.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/tunnel-agent-0.4.3.tgz_1462396470295_0.23639482469297945"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "_npmVersion": "2.15.3", "_phantomChildren": {}, "_requested": {"raw": "tunnel-agent@~0.4.1", "scope": null, "escapedName": "tunnel-agent", "name": "tunnel-agent", "rawSpec": "~0.4.1", "spec": ">=0.4.1 <0.5.0", "type": "range"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "_shasum": "6373db76909fe570e08d73583365ed828a74eeeb", "_shrinkwrap": null, "_spec": "tunnel-agent@~0.4.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "dependencies": {}, "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "devDependencies": {}, "directories": {}, "dist": {"shasum": "6373db76909fe570e08d73583365ed828a74eeeb", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz"}, "engines": {"node": "*"}, "files": ["index.js"], "gitHead": "e72d830f5ed388a2a71d37ce062c38e3fb34bdde", "homepage": "https://github.com/mikeal/tunnel-agent#readme", "license": "Apache-2.0", "main": "index.js", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "name": "tunnel-agent", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "scripts": {}, "version": "0.4.3"}