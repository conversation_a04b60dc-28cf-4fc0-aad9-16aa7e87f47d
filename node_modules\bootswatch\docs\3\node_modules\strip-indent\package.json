{"_args": [[{"raw": "strip-indent@^1.0.1", "scope": null, "escapedName": "strip-indent", "name": "strip-indent", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/redent"]], "_from": "strip-indent@>=1.0.1 <2.0.0", "_id": "strip-indent@1.0.1", "_inCache": true, "_location": "/strip-indent", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.5.1", "_phantomChildren": {}, "_requested": {"raw": "strip-indent@^1.0.1", "scope": null, "escapedName": "strip-indent", "name": "strip-indent", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "_requiredBy": ["/redent"], "_resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "_shasum": "0c7962a6adefa7bbd4ac366460a638552ae1a0a2", "_shrinkwrap": null, "_spec": "strip-indent@^1.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/redent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-indent": "cli.js"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "dependencies": {"get-stdin": "^4.0.1"}, "description": "Strip leading whitespace from every line in a string", "devDependencies": {"mocha": "*"}, "directories": {}, "dist": {"shasum": "0c7962a6adefa7bbd4ac366460a638552ae1a0a2", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "cli.js"], "gitHead": "addcf90a56001ea122e9f1254987016bc87e5b5f", "homepage": "https://github.com/sindresorhus/strip-indent", "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "strip-indent", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "scripts": {"test": "mocha"}, "version": "1.0.1"}