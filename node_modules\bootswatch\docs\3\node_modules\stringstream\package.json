{"_args": [[{"raw": "stringstream@~0.0.4", "scope": null, "escapedName": "stringstream", "name": "stringstream", "rawSpec": "~0.0.4", "spec": ">=0.0.4 <0.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/request"]], "_from": "stringstream@>=0.0.4 <0.1.0", "_id": "stringstream@0.0.5", "_inCache": true, "_location": "/stringstream", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.14.8", "_phantomChildren": {}, "_requested": {"raw": "stringstream@~0.0.4", "scope": null, "escapedName": "stringstream", "name": "stringstream", "rawSpec": "~0.0.4", "spec": ">=0.0.4 <0.1.0", "type": "range"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "_shasum": "4e484cd4de5a0bbbee18e46307710a8a81621878", "_shrinkwrap": null, "_spec": "stringstream@~0.0.4", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/mhart"}, "bugs": {"url": "https://github.com/mhart/StringStream/issues"}, "dependencies": {}, "description": "Encode and decode streams into string streams", "devDependencies": {}, "directories": {}, "dist": {"shasum": "4e484cd4de5a0bbbee18e46307710a8a81621878", "tarball": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz"}, "gitHead": "1efe3bf507bf3a1161f8473908b60e881d41422b", "homepage": "https://github.com/mhart/StringStream#readme", "keywords": ["string", "stream", "base64", "gzip"], "license": "MIT", "main": "stringstream.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "stringstream", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/mhart/StringStream.git"}, "scripts": {}, "version": "0.0.5"}