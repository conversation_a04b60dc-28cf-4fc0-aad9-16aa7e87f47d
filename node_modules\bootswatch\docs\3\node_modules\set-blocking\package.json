{"_args": [[{"raw": "set-blocking@~2.0.0", "scope": null, "escapedName": "set-blocking", "name": "set-blocking", "rawSpec": "~2.0.0", "spec": ">=2.0.0 <2.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/npmlog"]], "_from": "set-blocking@>=2.0.0 <2.1.0", "_id": "set-blocking@2.0.0", "_inCache": true, "_location": "/set-blocking", "_nodeVersion": "0.12.7", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/set-blocking-2.0.0.tgz_1463525966987_0.5456729622092098"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "2.11.3", "_phantomChildren": {}, "_requested": {"raw": "set-blocking@~2.0.0", "scope": null, "escapedName": "set-blocking", "name": "set-blocking", "rawSpec": "~2.0.0", "spec": ">=2.0.0 <2.1.0", "type": "range"}, "_requiredBy": ["/npmlog", "/yargs"], "_resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "_shasum": "045f9782d011ae9a6803ddd382b24392b3d890f7", "_shrinkwrap": null, "_spec": "set-blocking@~2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/npmlog", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "dependencies": {}, "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.9", "mocha": "^2.4.5", "nyc": "^6.4.4", "standard": "^7.0.1", "standard-version": "^2.2.1"}, "directories": {}, "dist": {"shasum": "045f9782d011ae9a6803ddd382b24392b3d890f7", "tarball": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"}, "files": ["index.js", "LICENSE.txt"], "gitHead": "7eec10577b5fff264de477ba3b9d07f404946eff", "homepage": "https://github.com/yargs/set-blocking#readme", "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "name": "set-blocking", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "test": "nyc mocha ./test/*.js", "version": "standard-version"}, "version": "2.0.0"}