{"_args": [[{"raw": "sass-graph@^2.1.1", "scope": null, "escapedName": "sass-graph", "name": "sass-graph", "rawSpec": "^2.1.1", "spec": ">=2.1.1 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass"]], "_from": "sass-graph@>=2.1.1 <3.0.0", "_id": "sass-graph@2.2.4", "_inCache": true, "_location": "/sass-graph", "_nodeVersion": "7.6.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sass-graph-2.2.4.tgz_1495012724834_0.36490066698752344"}, "_npmUser": {"name": "xzyfer", "email": "<EMAIL>"}, "_npmVersion": "4.1.2", "_phantomChildren": {"brace-expansion": "1.1.8", "fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "once": "1.4.0", "path-is-absolute": "1.0.1"}, "_requested": {"raw": "sass-graph@^2.1.1", "scope": null, "escapedName": "sass-graph", "name": "sass-graph", "rawSpec": "^2.1.1", "spec": ">=2.1.1 <3.0.0", "type": "range"}, "_requiredBy": ["/node-sass"], "_resolved": "https://registry.npmjs.org/sass-graph/-/sass-graph-2.2.4.tgz", "_shasum": "13fbd63cd1caf0908b9fd93476ad43a51d1e0b49", "_shrinkwrap": null, "_spec": "sass-graph@^2.1.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass", "author": {"name": "xzyfer"}, "bin": {"sassgraph": "bin/sassgraph"}, "bugs": {"url": "https://github.com/xzyfer/sass-graph/issues"}, "dependencies": {"glob": "^7.0.0", "lodash": "^4.0.0", "scss-tokenizer": "^0.2.3", "yargs": "^7.0.0"}, "description": "Parse sass files and extract a graph of imports", "devDependencies": {"assert": "^1.3.0", "chai": "^3.5.0", "coveralls": "^2.13.0", "mocha": "^3.2.0", "nyc": "^10.2.0"}, "directories": {"bin": "./bin"}, "dist": {"shasum": "13fbd63cd1caf0908b9fd93476ad43a51d1e0b49", "tarball": "https://registry.npmjs.org/sass-graph/-/sass-graph-2.2.4.tgz"}, "files": ["bin", "parse-imports.js", "sass-graph.js"], "gitHead": "f6aa19580600d9245516e20d588339824dacdb95", "homepage": "https://github.com/xzyfer/sass-graph#readme", "keywords": ["sass", "graph"], "license": "MIT", "main": "sass-graph.js", "maintainers": [{"name": "lox", "email": "<EMAIL>"}, {"name": "xzyfer", "email": "<EMAIL>"}], "name": "sass-graph", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/xzyfer/sass-graph.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "nyc mocha"}, "version": "2.2.4"}