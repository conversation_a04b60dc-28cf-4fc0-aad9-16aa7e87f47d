{"_args": [[{"raw": "sprintf-js@~1.0.2", "scope": null, "escapedName": "sprintf-js", "name": "sprintf-js", "rawSpec": "~1.0.2", "spec": ">=1.0.2 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/cosmiconfig/node_modules/argparse"]], "_from": "sprintf-js@>=1.0.2 <1.1.0", "_id": "sprintf-js@1.0.3", "_inCache": true, "_location": "/sprintf-js", "_nodeVersion": "0.12.4", "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "_npmVersion": "2.10.1", "_phantomChildren": {}, "_requested": {"raw": "sprintf-js@~1.0.2", "scope": null, "escapedName": "sprintf-js", "name": "sprintf-js", "rawSpec": "~1.0.2", "spec": ">=1.0.2 <1.1.0", "type": "range"}, "_requiredBy": ["/cosmiconfig/argparse"], "_resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "_shasum": "04e6926f662895354f3dd015203633b857297e2c", "_shrinkwrap": null, "_spec": "sprintf-js@~1.0.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/cosmiconfig/node_modules/argparse", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://alexei.ro/"}, "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dependencies": {}, "description": "JavaScript sprintf implementation", "devDependencies": {"grunt": "*", "grunt-contrib-uglify": "*", "grunt-contrib-watch": "*", "mocha": "*"}, "directories": {}, "dist": {"shasum": "04e6926f662895354f3dd015203633b857297e2c", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"}, "gitHead": "747b806c2dab5b64d5c9958c42884946a187c3b1", "homepage": "https://github.com/alexei/sprintf.js#readme", "license": "BSD-3-<PERSON><PERSON>", "main": "src/sprintf.js", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "name": "sprintf-js", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/alexei/sprintf.js.git"}, "scripts": {"test": "mocha test/test.js"}, "version": "1.0.3"}