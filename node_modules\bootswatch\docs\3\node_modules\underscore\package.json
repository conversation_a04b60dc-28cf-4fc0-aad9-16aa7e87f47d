{"_args": [[{"raw": "underscore@~1.7.0", "scope": null, "escapedName": "underscore", "name": "underscore", "rawSpec": "~1.7.0", "spec": ">=1.7.0 <1.8.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/argparse"]], "_from": "underscore@>=1.7.0 <1.8.0", "_id": "underscore@1.7.0", "_inCache": true, "_location": "/underscore", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_npmVersion": "1.4.24", "_phantomChildren": {}, "_requested": {"raw": "underscore@~1.7.0", "scope": null, "escapedName": "underscore", "name": "underscore", "rawSpec": "~1.7.0", "spec": ">=1.7.0 <1.8.0", "type": "range"}, "_requiredBy": ["/argparse"], "_resolved": "https://registry.npmjs.org/underscore/-/underscore-1.7.0.tgz", "_shasum": "6bbaf0877500d36be34ecaa584e0db9fef035209", "_shrinkwrap": null, "_spec": "underscore@~1.7.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jashkenas/underscore/issues"}, "dependencies": {}, "description": "JavaScript's functional programming helper library.", "devDependencies": {"docco": "0.6.x", "eslint": "0.6.x", "phantomjs": "1.9.7-1", "uglify-js": "2.4.x"}, "directories": {}, "dist": {"shasum": "6bbaf0877500d36be34ecaa584e0db9fef035209", "tarball": "https://registry.npmjs.org/underscore/-/underscore-1.7.0.tgz"}, "files": ["underscore.js", "underscore-min.js", "LICENSE"], "gitHead": "da996e665deb0b69b257e80e3e257c04fde4191c", "homepage": "http://underscorejs.org", "keywords": ["util", "functional", "server", "client", "browser"], "licenses": [{"type": "MIT", "url": "https://raw.github.com/jashkenas/underscore/master/LICENSE"}], "main": "underscore.js", "maintainers": [{"name": "jashkenas", "email": "<EMAIL>"}], "name": "underscore", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/jashkenas/underscore.git"}, "scripts": {"build": "uglifyjs underscore.js -c \"evaluate=false\" --comments \"/    .*/\" -m --source-map underscore-min.map -o underscore-min.js", "doc": "docco underscore.js", "test": "phantomjs test/vendor/runner.js test/index.html?noglobals=true && eslint underscore.js test/*.js test/vendor/runner.js"}, "version": "1.7.0"}