{"_args": [[{"raw": "rimraf@~2.2.8", "scope": null, "escapedName": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "rawSpec": "~2.2.8", "spec": ">=2.2.8 <2.3.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/grunt"]], "_from": "rimraf@>=2.2.8 <2.3.0", "_id": "rimraf@2.2.8", "_inCache": true, "_location": "/rimraf", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "1.4.10", "_phantomChildren": {}, "_requested": {"raw": "rimraf@~2.2.8", "scope": null, "escapedName": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "rawSpec": "~2.2.8", "spec": ">=2.2.8 <2.3.0", "type": "range"}, "_requiredBy": ["/grunt"], "_resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "_shasum": "e439be2aaee327321952730f99a8929e4fc50582", "_shrinkwrap": null, "_spec": "rimraf@~2.2.8", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/grunt", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"rimraf": "./bin.js"}, "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/wvl"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "A deep deletion module for node (like `rm -rf`)", "devDependencies": {}, "directories": {}, "dist": {"shasum": "e439be2aaee327321952730f99a8929e4fc50582", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz"}, "homepage": "https://github.com/isaacs/rimraf", "license": {"type": "MIT", "url": "https://github.com/isaacs/rimraf/raw/master/LICENSE"}, "main": "rimraf.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "<PERSON><PERSON><PERSON>", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "scripts": {"test": "cd test && bash run.sh"}, "version": "2.2.8"}