{"_args": [[{"raw": "shell-quote@^1.6.1", "scope": null, "escapedName": "shell-quote", "name": "shell-quote", "rawSpec": "^1.6.1", "spec": ">=1.6.1 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/npm-run-all"]], "_from": "shell-quote@>=1.6.1 <2.0.0", "_id": "shell-quote@1.6.1", "_inCache": true, "_location": "/shell-quote", "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/shell-quote-1.6.1.tgz_1466196190331_0.3792361367959529"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_npmVersion": "3.7.1", "_phantomChildren": {}, "_requested": {"raw": "shell-quote@^1.6.1", "scope": null, "escapedName": "shell-quote", "name": "shell-quote", "rawSpec": "^1.6.1", "spec": ">=1.6.1 <2.0.0", "type": "range"}, "_requiredBy": ["/npm-run-all"], "_resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz", "_shasum": "f4781949cce402697127430ea3b3c5476f481767", "_shrinkwrap": null, "_spec": "shell-quote@^1.6.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/npm-run-all", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dependencies": {"array-filter": "~0.0.0", "array-map": "~0.0.0", "array-reduce": "~0.0.0", "jsonify": "~0.0.0"}, "description": "quote and parse shell commands", "devDependencies": {"tape": "~2.3.0"}, "directories": {}, "dist": {"shasum": "f4781949cce402697127430ea3b3c5476f481767", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz"}, "gitHead": "09935581cd2b300d74a65bda3b1cbeb52779dd16", "homepage": "https://github.com/substack/node-shell-quote#readme", "keywords": ["shell", "command", "quote", "parse"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "name": "shell-quote", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/substack/node-shell-quote.git"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.6.1"}