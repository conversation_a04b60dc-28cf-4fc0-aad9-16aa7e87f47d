{"_args": [[{"raw": "repeat-string@^1.5.2", "scope": null, "escapedName": "repeat-string", "name": "repeat-string", "rawSpec": "^1.5.2", "spec": ">=1.5.2 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/fill-range"]], "_from": "repeat-string@>=1.5.2 <2.0.0", "_id": "repeat-string@1.6.1", "_inCache": true, "_location": "/repeat-string", "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/repeat-string-1.6.1.tgz_1477241638674_0.3764322670176625"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.3", "_phantomChildren": {}, "_requested": {"raw": "repeat-string@^1.5.2", "scope": null, "escapedName": "repeat-string", "name": "repeat-string", "rawSpec": "^1.5.2", "spec": ">=1.5.2 <2.0.0", "type": "range"}, "_requiredBy": ["/fill-range"], "_resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "_shasum": "8dcae470e1c88abc2d600fff4a776286da75e637", "_shrinkwrap": null, "_spec": "repeat-string@^1.5.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/fill-range", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/repeat-string/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/doowb"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://linus.unnebäck.se"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tbusser.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "wooorm.com"}], "dependencies": {}, "description": "Repeat the given string n times. Fastest implementation for repeating a string.", "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "gulp-format-md": "^0.1.11", "isobject": "^2.1.0", "mocha": "^3.1.2", "repeating": "^3.0.0", "text-table": "^0.2.0", "yargs-parser": "^4.0.2"}, "directories": {}, "dist": {"shasum": "8dcae470e1c88abc2d600fff4a776286da75e637", "tarball": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"}, "engines": {"node": ">=0.10"}, "files": ["index.js"], "gitHead": "1a95c5d99a02999ccd2cf4663959a18bd2def7b8", "homepage": "https://github.com/jonschlinkert/repeat-string", "keywords": ["fast", "fastest", "fill", "left", "left-pad", "multiple", "pad", "padding", "repeat", "repeating", "repetition", "right", "right-pad", "string", "times"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "name": "repeat-string", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/repeat-string.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["repeat-element"]}, "helpers": ["./benchmark/helper.js"], "reflinks": ["verb"]}, "version": "1.6.1"}