{"_args": [[{"raw": "sigmund@~1.0.0", "scope": null, "escapedName": "sigmund", "name": "sigmund", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/findup-sync/node_modules/minimatch"]], "_from": "sigmund@>=1.0.0 <1.1.0", "_id": "sigmund@1.0.1", "_inCache": true, "_location": "/sigmund", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "2.10.0", "_phantomChildren": {}, "_requested": {"raw": "sigmund@~1.0.0", "scope": null, "escapedName": "sigmund", "name": "sigmund", "rawSpec": "~1.0.0", "spec": ">=1.0.0 <1.1.0", "type": "range"}, "_requiredBy": ["/findup-sync/minimatch", "/minimatch"], "_resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "_shasum": "3ff21f198cad2175f9f3b781853fd94d0d19b590", "_shrinkwrap": null, "_spec": "sigmund@~1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/findup-sync/node_modules/minimatch", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/sigmund/issues"}, "dependencies": {}, "description": "Quick and dirty signatures for Objects.", "devDependencies": {"tap": "~0.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "3ff21f198cad2175f9f3b781853fd94d0d19b590", "tarball": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz"}, "gitHead": "527f97aa5bb253d927348698c0cd3bb267d098c6", "homepage": "https://github.com/isaacs/sigmund#readme", "keywords": ["object", "signature", "key", "data", "psychoanalysis"], "license": "ISC", "main": "sigmund.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "sigmund", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/isaacs/sigmund.git"}, "scripts": {"bench": "node bench.js", "test": "tap test/*.js"}, "version": "1.0.1"}