{"_args": [[{"raw": "underscore.string@~2.2.1", "scope": null, "escapedName": "underscore.string", "name": "underscore.string", "rawSpec": "~2.2.1", "spec": ">=2.2.1 <2.3.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/grunt"]], "_from": "underscore.string@>=2.2.1 <2.3.0", "_id": "underscore.string@2.2.1", "_inCache": true, "_location": "/underscore.string", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "_npmVersion": "1.2.32", "_phantomChildren": {}, "_requested": {"raw": "underscore.string@~2.2.1", "scope": null, "escapedName": "underscore.string", "name": "underscore.string", "rawSpec": "~2.2.1", "spec": ">=2.2.1 <2.3.0", "type": "range"}, "_requiredBy": ["/grunt", "/grunt-legacy-util"], "_resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.2.1.tgz", "_shasum": "d7c0fa2af5d5a1a67f4253daee98132e733f0f19", "_shrinkwrap": null, "_spec": "underscore.string@~2.2.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/grunt", "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "String manipulation extensions for Underscore.js javascript library.", "devDependencies": {}, "directories": {"lib": "./lib"}, "dist": {"shasum": "d7c0fa2af5d5a1a67f4253daee98132e733f0f19", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.2.1.tgz"}, "engines": {"node": "*"}, "homepage": "http://epeli.github.com/underscore.string/", "keywords": ["underscore", "string"], "licenses": [{"type": "MIT"}], "main": "./lib/underscore.string", "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}], "name": "underscore.string", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "version": "2.2.1"}