{"_args": [[{"raw": "stdout-stream@^1.4.0", "scope": null, "escapedName": "stdout-stream", "name": "stdout-stream", "rawSpec": "^1.4.0", "spec": ">=1.4.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass"]], "_from": "stdout-stream@>=1.4.0 <2.0.0", "_id": "stdout-stream@1.4.0", "_inCache": true, "_location": "/stdout-stream", "_nodeVersion": "0.12.4", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.10.1", "_phantomChildren": {}, "_requested": {"raw": "stdout-stream@^1.4.0", "scope": null, "escapedName": "stdout-stream", "name": "stdout-stream", "rawSpec": "^1.4.0", "spec": ">=1.4.0 <2.0.0", "type": "range"}, "_requiredBy": ["/node-sass"], "_resolved": "https://registry.npmjs.org/stdout-stream/-/stdout-stream-1.4.0.tgz", "_shasum": "a2c7c8587e54d9427ea9edb3ac3f2cd522df378b", "_shrinkwrap": null, "_spec": "stdout-stream@^1.4.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/node-sass", "bugs": {"url": "https://github.com/mafintosh/stdout-stream/issues"}, "dependencies": {"readable-stream": "^2.0.1"}, "description": "Non-blocking stdout stream", "devDependencies": {"tape": "~2.12.3"}, "directories": {}, "dist": {"shasum": "a2c7c8587e54d9427ea9edb3ac3f2cd522df378b", "tarball": "https://registry.npmjs.org/stdout-stream/-/stdout-stream-1.4.0.tgz"}, "gitHead": "0a65e6cdfadc5043a627d6d074f4f064f5f2d188", "homepage": "https://github.com/mafintosh/stdout-stream#readme", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "stdout-stream", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/stdout-stream.git"}, "scripts": {"test": "tape test/index.js"}, "version": "1.4.0"}