const mongoose = require('mongoose');
const { User, Trip, EmergencyNumber, ChatMessage, Booking } = require('./schemas');

// Connect to MongoDB
mongoose.connect('mongodb://localhost/travel_app', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Sample Data Insertion
async function seedDatabase() {
  try {
    // Clear existing data
    await User.deleteMany({});
    await Trip.deleteMany({});
    await EmergencyNumber.deleteMany({});
    await ChatMessage.deleteMany({});
    await Booking.deleteMany({});

    // Insert Users
    const user1 = new User({
      email: '<EMAIL>',
      phone: '+************',
      password: 'password123', // Will be hashed by pre-save hook
      profile: {
        name: '<PERSON>',
        personal_details: { address: '123 Street, India', dob: new Date('1990-01-01') },
        subscription_history: [{ plan: 'Free', start_date: new Date() }],
      },
      friends: [{ uid: 'friend123', activities_count: 2 }],
    });
    const user2 = new User({
      email: '<EMAIL>',
      googleId: 'google123',
      profile: { name: 'Jane Smith', personal_details: { dob: new Date('1992-02-02') } },
    });
    await user1.save();
    await user2.save();

    // Insert Bookings
    const booking1 = new Booking({
      type: 'package',
      details: {
        name: 'Rishikesh Adventure',
        price: 45000,
        destination: 'Rishikesh',
        activities: ['rafting', 'trekking'],
        duration: '5 days',
        url: 'https://www.makemytrip.com/package/123',
      },
      source: 'scraped',
    });
    await booking1.save();

    // Insert Trips
    const trip1 = new Trip({
      user_id: user1._id,
      title: 'Rishikesh Adventure Trip',
      destination: 'Rishikesh, India',
      budget: 100000,
      start_date: new Date('2025-10-01'),
      end_date: new Date('2025-10-05'),
      expenses: [
        { amount: 5000, description: 'Hotel stay', split_with: [user2._id], created_at: new Date() },
      ],
      friends: [user2._id],
      photos: [
        { url: 'https://storage.example.com/photo1.jpg', uploaded_by: user1._id, uploaded_at: new Date() },
      ],
      bookings: [
        { type: 'package', details: booking1.details, booking_id: booking1._id, created_at: new Date() },
      ],
      status: 'upcoming',
    });
    await trip1.save();

    // Insert Emergency Numbers
    const emergency1 = new EmergencyNumber({
      country: 'India',
      police: '100',
      fire: '101',
      ambulance: '108',
    });
    await emergency1.save();

    // Insert Chat Messages
    const chat1 = new ChatMessage({
      trip_id: trip1._id,
      sender_id: user1._id,
      message: 'Hey, excited for the Rishikesh trip!',
      type: 'text',
    });
    const chat2 = new ChatMessage({
      sender_id: 'bot',
      message: 'I found a ₹45,000 adventure package for Rishikesh!',
      type: 'text',
    });
    await chat1.save();
    await chat2.save();

    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    mongoose.connection.close();
  }
}

seedDatabase();