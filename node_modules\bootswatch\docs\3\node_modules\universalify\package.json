{"_args": [[{"raw": "universalify@^0.1.0", "scope": null, "escapedName": "universalify", "name": "universalify", "rawSpec": "^0.1.0", "spec": ">=0.1.0 <0.2.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/fs-extra"]], "_from": "universalify@>=0.1.0 <0.2.0", "_id": "universalify@0.1.1", "_inCache": true, "_location": "/universalify", "_nodeVersion": "7.8.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify-0.1.1.tgz_1500561668015_0.5723595882300287"}, "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "_npmVersion": "4.2.0", "_phantomChildren": {}, "_requested": {"raw": "universalify@^0.1.0", "scope": null, "escapedName": "universalify", "name": "universalify", "rawSpec": "^0.1.0", "spec": ">=0.1.0 <0.2.0", "type": "range"}, "_requiredBy": ["/fs-extra"], "_resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.1.tgz", "_shasum": "fa71badd4437af4c148841e3b3b165f9e9e590b7", "_shrinkwrap": null, "_spec": "universalify@^0.1.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/fs-extra", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "dependencies": {}, "description": "Make a callback- or promise-based function support both promises and callbacks.", "devDependencies": {"colortape": "^0.1.2", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "directories": {}, "dist": {"shasum": "fa71badd4437af4c148841e3b3b165f9e9e590b7", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.1.1.tgz"}, "files": ["index.js"], "gitHead": "6dcade29ad1fc945f3d2a6e63cf92ec041fa83d2", "homepage": "https://github.com/RyanZim/universalify#readme", "keywords": ["callback", "native", "promise"], "license": "MIT", "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "name": "universalify", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "version": "0.1.1"}