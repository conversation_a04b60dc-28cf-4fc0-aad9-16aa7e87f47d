{"_args": [[{"raw": "safe-buffer@~5.1.1", "scope": null, "escapedName": "safe-buffer", "name": "safe-buffer", "rawSpec": "~5.1.1", "spec": ">=5.1.1 <5.2.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream"]], "_from": "safe-buffer@>=5.1.1 <5.2.0", "_id": "safe-buffer@5.1.1", "_inCache": true, "_location": "/safe-buffer", "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer-5.1.1.tgz_1498076368476_0.22441886644810438"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "_npmVersion": "5.0.3", "_phantomChildren": {}, "_requested": {"raw": "safe-buffer@~5.1.1", "scope": null, "escapedName": "safe-buffer", "name": "safe-buffer", "rawSpec": "~5.1.1", "spec": ">=5.1.1 <5.2.0", "type": "range"}, "_requiredBy": ["/readable-stream", "/string_decoder"], "_resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "_shasum": "893312af69b2123def71f57889001671eeb2c853", "_shrinkwrap": null, "_spec": "safe-buffer@~5.1.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "dependencies": {}, "description": "Safer Node.js Buffer API", "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "directories": {}, "dist": {"integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "shasum": "893312af69b2123def71f57889001671eeb2c853", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz"}, "gitHead": "5261e0c19dd820c31dd21cb4116902b0ed0f9e57", "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "safe-buffer", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "version": "5.1.1"}