{"_args": [[{"raw": "uuid@^3.0.0", "scope": null, "escapedName": "uuid", "name": "uuid", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/request"]], "_from": "uuid@>=3.0.0 <4.0.0", "_id": "uuid@3.0.1", "_inCache": true, "_location": "/uuid", "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/uuid-3.0.1.tgz_1480403886767_0.2584113120101392"}, "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "_npmVersion": "3.10.3", "_phantomChildren": {}, "_requested": {"raw": "uuid@^3.0.0", "scope": null, "escapedName": "uuid", "name": "uuid", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/uuid/-/uuid-3.0.1.tgz", "_shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "_shrinkwrap": null, "_spec": "uuid@^3.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/request", "bin": {"uuid": "./bin/uuid"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js"}, "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "RFC4122 (v1 and v4) generator", "devDependencies": {"mocha": "3.1.2"}, "directories": {}, "dist": {"shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.0.1.tgz"}, "gitHead": "374de826de71d8997f71b4641f65552e48956ced", "homepage": "https://github.com/kelektiv/node-uuid#readme", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "name": "uuid", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "scripts": {"test": "mocha test/test.js"}, "version": "3.0.1"}