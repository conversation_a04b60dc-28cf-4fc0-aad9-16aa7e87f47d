{"_args": [[{"raw": "shebang-regex@^1.0.0", "scope": null, "escapedName": "shebang-regex", "name": "shebang-regex", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/shebang-command"]], "_from": "shebang-regex@>=1.0.0 <2.0.0", "_id": "shebang-regex@1.0.0", "_inCache": true, "_location": "/shebang-regex", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.5.1", "_phantomChildren": {}, "_requested": {"raw": "shebang-regex@^1.0.0", "scope": null, "escapedName": "shebang-regex", "name": "shebang-regex", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/shebang-command"], "_resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "_shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "_shrinkwrap": null, "_spec": "shebang-regex@^1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/shebang-command", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "dependencies": {}, "description": "Regular expression for matching a shebang", "devDependencies": {"ava": "0.0.4"}, "directories": {}, "dist": {"shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "cb774c70d5f569479ca997abf8ee7e558e617284", "homepage": "https://github.com/sindresorhus/shebang-regex", "keywords": ["re", "regex", "regexp", "shebang", "match", "test"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "shebang-regex", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}