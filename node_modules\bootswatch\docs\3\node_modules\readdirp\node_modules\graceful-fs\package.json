{"_args": [[{"raw": "graceful-fs@^4.1.2", "scope": null, "escapedName": "graceful-fs", "name": "graceful-fs", "rawSpec": "^4.1.2", "spec": ">=4.1.2 <5.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/readdirp"]], "_from": "graceful-fs@>=4.1.2 <5.0.0", "_id": "graceful-fs@4.1.11", "_inCache": true, "_location": "/readdirp/graceful-fs", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.11.tgz_1479843029430_0.2122855328489095"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.9", "_phantomChildren": {}, "_requested": {"raw": "graceful-fs@^4.1.2", "scope": null, "escapedName": "graceful-fs", "name": "graceful-fs", "rawSpec": "^4.1.2", "spec": ">=4.1.2 <5.0.0", "type": "range"}, "_requiredBy": ["/readdirp"], "_resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "_shasum": "0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658", "_shrinkwrap": null, "_spec": "graceful-fs@^4.1.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/readdirp", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "dependencies": {}, "description": "A drop-in replacement for fs, making various improvements.", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz"}, "engines": {"node": ">=0.4.0"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "65cf80d1fd3413b823c16c626c1e7c326452bee5", "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "main": "graceful-fs.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "graceful-fs", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "scripts": {"test": "node test.js | tap -"}, "version": "4.1.11"}