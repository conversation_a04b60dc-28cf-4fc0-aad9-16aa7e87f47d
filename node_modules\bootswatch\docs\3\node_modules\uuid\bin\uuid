#!/usr/bin/env node

var path = require('path');
var uuid = require(path.join(__dirname, '..'));

var arg = process.argv[2];

if ('--help' === arg) {
  console.log('\n  USAGE: uuid [version] [options]\n\n');
  console.log('  options:\n');
  console.log('  --help                     Display this message and exit\n');
  process.exit(0);
}

if (null == arg) {
  console.log(uuid());
  process.exit(0);
}

if ('v1' !== arg && 'v4' !== arg) {
  console.error('Version must be RFC4122 version 1 or version 4, denoted as "v1" or "v4"');
  process.exit(1);
}

console.log(uuid[arg]());
process.exit(0);
