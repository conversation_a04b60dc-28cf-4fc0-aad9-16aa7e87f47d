{"_args": [[{"raw": "require-directory@^2.1.1", "scope": null, "escapedName": "require-directory", "name": "require-directory", "rawSpec": "^2.1.1", "spec": ">=2.1.1 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/yargs"]], "_from": "require-directory@>=2.1.1 <3.0.0", "_id": "require-directory@2.1.1", "_inCache": true, "_location": "/require-directory", "_nodeVersion": "0.12.0", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "_npmVersion": "2.5.1", "_phantomChildren": {}, "_requested": {"raw": "require-directory@^2.1.1", "scope": null, "escapedName": "require-directory", "name": "require-directory", "rawSpec": "^2.1.1", "spec": ">=2.1.1 <3.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "_shasum": "8c64ad5fd30dab1c976e2344ffe7f792a6a6df42", "_shrinkwrap": null, "_spec": "require-directory@^2.1.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "dependencies": {}, "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "directories": {}, "dist": {"shasum": "8c64ad5fd30dab1c976e2344ffe7f792a6a6df42", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"}, "engines": {"node": ">=0.10.0"}, "gitHead": "cc71c23dd0c16cefd26855303c16ca1b9b50a36d", "homepage": "https://github.com/troygoode/node-require-directory/", "keywords": ["require", "directory", "library", "recursive"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "name": "require-directory", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "scripts": {"lint": "jshint index.js test/test.js", "test": "mocha"}, "version": "2.1.1"}