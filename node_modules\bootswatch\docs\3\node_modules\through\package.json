{"_args": [[{"raw": "through@~2.3.1", "scope": null, "escapedName": "through", "name": "through", "rawSpec": "~2.3.1", "spec": ">=2.3.1 <2.4.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream"]], "_from": "through@>=2.3.1 <2.4.0", "_id": "through@2.3.8", "_inCache": true, "_location": "/through", "_nodeVersion": "2.3.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_npmVersion": "2.12.0", "_phantomChildren": {}, "_requested": {"raw": "through@~2.3.1", "scope": null, "escapedName": "through", "name": "through", "rawSpec": "~2.3.1", "spec": ">=2.3.1 <2.4.0", "type": "range"}, "_requiredBy": ["/event-stream", "/pause-stream", "/split"], "_resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "_shasum": "0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5", "_shrinkwrap": null, "_spec": "through@~2.3.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/event-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/through/issues"}, "dependencies": {}, "description": "simplified stream construction", "devDependencies": {"from": "~0.1.3", "stream-spec": "~0.3.5", "tape": "~2.3.2"}, "directories": {}, "dist": {"shasum": "0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5", "tarball": "https://registry.npmjs.org/through/-/through-2.3.8.tgz"}, "gitHead": "2c5a6f9a0cc54da759b6e10964f2081c358e49dc", "homepage": "https://github.com/dominictarr/through", "keywords": ["stream", "streams", "user-streams", "pipe"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "name": "through", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/dominictarr/through.git"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "testling": {"browsers": ["ie/8..latest", "ff/15..latest", "chrome/20..latest", "safari/5.1..latest"], "files": "test/*.js"}, "version": "2.3.8"}