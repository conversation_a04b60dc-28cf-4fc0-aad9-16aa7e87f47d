{"_args": [[{"raw": "readdirp@^2.0.0", "scope": null, "escapedName": "readdirp", "name": "readdirp", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/chokidar"]], "_from": "readdirp@>=2.0.0 <3.0.0", "_id": "readdirp@2.1.0", "_inCache": true, "_location": "/readdirp", "_nodeVersion": "4.4.6", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/readdirp-2.1.0.tgz_1467053820730_0.8782131769694388"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "_npmVersion": "2.15.6", "_phantomChildren": {"brace-expansion": "1.1.8"}, "_requested": {"raw": "readdirp@^2.0.0", "scope": null, "escapedName": "readdirp", "name": "readdirp", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "_shasum": "4ed0ad060df3073300c48440373f72d1cc642d78", "_shrinkwrap": null, "_spec": "readdirp@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/chokidar", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dependencies": {"graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "description": "Recursive version of fs.readdir with streaming api.", "devDependencies": {"nave": "^0.5.1", "proxyquire": "^1.7.9", "tap": "1.3.2", "through2": "^2.0.0"}, "directories": {}, "dist": {"shasum": "4ed0ad060df3073300c48440373f72d1cc642d78", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz"}, "engines": {"node": ">=0.6"}, "gitHead": "5a3751f86a1c2bbbb8e3a42685d4191992631e6c", "homepage": "https://github.com/thlorenz/readdirp", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "license": "MIT", "main": "readdirp.js", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "name": "readdirp", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/thlorenz/readdirp.git"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-4": "nave use 4.4 npm run test-main", "test-6": "nave use 6.2 npm run test-main", "test-all": "npm run test-main && npm run test-0.10 && npm run test-0.12 && npm run test-4 && npm run test-6", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "version": "2.1.0"}