<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Underscore.strings Test Suite</title>
  <link rel="stylesheet" href="test_underscore/vendor/qunit.css" type="text/css" media="screen" />
  <script type="text/javascript" src="test_underscore/vendor/jquery.js"></script>
  <script type="text/javascript" src="test_underscore/vendor/qunit.js"></script>
  <script type="text/javascript" src="test_underscore/vendor/jslitmus.js"></script>
  <script type="text/javascript" src="underscore.js"></script>
  <script type="text/javascript" src="../lib/underscore.string.js"></script>
  <script type="text/javascript" src="strings.js"></script>
  <script type="text/javascript" src="speed.js"></script>
</head>
<body>
  <h1 id="qunit-header">Underscore.string Test Suite</h1>
  <h2 id="qunit-banner"></h2>
  <h2 id="qunit-userAgent"></h2>
  <ol id="qunit-tests"></ol>
  <br />
  <h1 class="qunit-header">Underscore.string Speed Suite</h1>
  <!-- <h2 class="qunit-userAgent">
    A representative sample of the functions are benchmarked here, to provide
    a sense of how fast they might run in different browsers.
    Each iteration runs on an array of 1000 elements.<br /><br />
    For example, the 'intersect' test measures the number of times you can
    find the intersection of two thousand-element arrays in one second.
  </h2> -->
  <br />
</body>
</html>
