{"_args": [[{"raw": "string_decoder@~1.0.3", "scope": null, "escapedName": "string_decoder", "name": "string_decoder", "rawSpec": "~1.0.3", "spec": ">=1.0.3 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream"]], "_from": "string_decoder@>=1.0.3 <1.1.0", "_id": "string_decoder@1.0.3", "_inCache": true, "_location": "/string_decoder", "_nodeVersion": "8.1.1", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder-1.0.3.tgz_1498156574101_0.8198789858724922"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "_npmVersion": "5.0.3", "_phantomChildren": {}, "_requested": {"raw": "string_decoder@~1.0.3", "scope": null, "escapedName": "string_decoder", "name": "string_decoder", "rawSpec": "~1.0.3", "spec": ">=1.0.3 <1.1.0", "type": "range"}, "_requiredBy": ["/readable-stream"], "_resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz", "_shasum": "0fc67d7c141825de94282dd536bec6b9bce860ab", "_shrinkwrap": null, "_spec": "string_decoder@~1.0.3", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/readable-stream", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "dependencies": {"safe-buffer": "~5.1.0"}, "description": "The string_decoder module from Node core", "devDependencies": {"babel-polyfill": "^6.23.0", "tap": "~0.4.8"}, "directories": {}, "dist": {"integrity": "sha512-4AH6Z5fzNNBcH+6XDMfA/BTt87skxqJlO0lAh3Dker5zThcAxG6mKz+iGu308UKoPPQ8Dcqx/4JhujzltRa+hQ==", "shasum": "0fc67d7c141825de94282dd536bec6b9bce860ab", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz"}, "gitHead": "e97f24dd3d047b72b9836518e2a0788e2a6a2fdb", "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "main": "lib/string_decoder.js", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "name": "string_decoder", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies"}, "version": "1.0.3"}