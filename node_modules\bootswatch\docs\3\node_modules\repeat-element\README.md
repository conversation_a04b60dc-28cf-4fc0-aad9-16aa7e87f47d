# repeat-element [![NPM version](https://badge.fury.io/js/repeat-element.svg)](http://badge.fury.io/js/repeat-element)

> Create an array by repeating the given value n times.

## Install

Install with [npm](https://www.npmjs.com/)

```bash
npm i repeat-element --save
```

## Usage

```js
var repeat = require('repeat-element');

repeat('a', 5);
//=> ['a', 'a', 'a', 'a', 'a']

repeat('a', 1);
//=> ['a']

repeat('a', 0);
//=> []

repeat(null, 5)
//» [ null, null, null, null, null ]

repeat({some: 'object'}, 5)
//» [ { some: 'object' },
//    { some: 'object' },
//    { some: 'object' },
//    { some: 'object' },
//    { some: 'object' } ]

repeat(5, 5)
//» [ 5, 5, 5, 5, 5 ]
```

## Related projects

[repeat-string](https://github.com/jonschlinkert/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string.

## Running tests

Install dev dependencies:

```bash
npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/repeat-element/issues)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright (c) 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on May 06, 2015._
