const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const Schema = mongoose.Schema;

// Connect to MongoDB (replace with MongoDB Atlas URI for production)
mongoose.connect('mongodb://localhost/travel_app', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Users Schema: Stores authentication, profile, and friend connections
const userSchema = new Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email'],
  },
  phone: {
    type: String,
    unique: true,
    sparse: true, // Allows null for users not using phone login
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number'],
  },
  password: {
    type: String,
    required: [function() { return !this.googleId && !this.appleId; }, 'Password is required for email login'],
    minlength: [8, 'Password must be at least 8 characters'],
  },
  googleId: { type: String, sparse: true }, // Google Sign-in ID
  appleId: { type: String, sparse: true }, // Apple Sign-in ID
  profile: {
    name: { type: String, required: [true, 'Name is required'], trim: true },
    personal_details: {
      address: { type: String, trim: true },
      dob: { type: Date },
    },
    subscription_history: [{
      plan: { type: String, enum: ['Free', 'Premium'], default: 'Free' },
      start_date: { type: Date, default: Date.now },
      end_date: { type: Date },
    }],
  },
  friends: [{
    uid: { type: String, required: true }, // Reference to another user’s _id
    activities_count: { type: Number, default: 0 },
  }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (this.isModified('password') && this.password) {
    this.password = await bcrypt.hash(this.password, 10);
  }
  this.updated_at = Date.now();
  next();
});

// Indexes for Users
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ phone: 1 }, { unique: true, sparse: true });
userSchema.index({ 'friends.uid': 1 });

// Trips Schema: Stores trip details, expenses, friends, and photos
const tripSchema = new Schema({
  user_id: { type: String, required: [true, 'User ID is required'] },
  title: { type: String, required: [true, 'Trip title is required'], trim: true },
  destination: { type: String, required: [true, 'Destination is required'], trim: true },
  budget: { type: Number, required: [true, 'Budget is required'], min: [0, 'Budget cannot be negative'] },
  start_date: { type: Date, required: [true, 'Start date is required'] },
  end_date: { type: Date, required: [true, 'End date is required'] },
  expenses: [{
    amount: { type: Number, required: true, min: 0 },
    description: { type: String, trim: true },
    split_with: [{ type: String }], // User IDs for expense splitting
    created_at: { type: Date, default: Date.now },
  }],
  friends: [{ type: String }], // User IDs of friends on the trip
  photos: [{
    url: { type: String, required: true }, // Firebase Storage/AWS S3 URL
    uploaded_by: { type: String, required: true },
    uploaded_at: { type: Date, default: Date.now },
  }],
  bookings: [{
    type: { type: String, enum: ['flight', 'hotel', 'package', 'activity'], required: true },
    details: { type: Object, required: true }, // From MakeMyTrip
    booking_id: { type: String }, // Reference to bookings collection
    created_at: { type: Date, default: Date.now },
  }],
  status: { type: String, enum: ['past', 'upcoming'], required: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

// Indexes for Trips
tripSchema.index({ user_id: 1 });
tripSchema.index({ status: 1 });
tripSchema.index({ destination: 1 });

// Update timestamp
tripSchema.pre('save', function(next) {
  this.updated_at = Date.now();
  next();
});

// Emergency Numbers Schema: Stores country-specific emergency contacts
const emergencyNumberSchema = new Schema({
  country: { type: String, required: [true, 'Country is required'], unique: true, trim: true },
  police: { type: String, required: [true, 'Police number is required'] },
  fire: { type: String, required: [true, 'Fire number is required'] },
  ambulance: { type: String, required: [true, 'Ambulance number is required'] },
  updated_at: { type: Date, default: Date.now },
});

// Index for Emergency Numbers
emergencyNumberSchema.index({ country: 1 }, { unique: true });

// Update timestamp
emergencyNumberSchema.pre('save', function(next) {
  this.updated_at = Date.now();
  next();
});

// Chat Messages Schema: Stores chatbot and friend group messages
const chatMessageSchema = new Schema({
  trip_id: { type: String }, // Optional for chatbot messages
  sender_id: { type: String, required: [true, 'Sender ID is required'] },
  message: { type: String, required: [true, 'Message is required'], trim: true },
  type: { type: String, enum: ['text', 'itinerary', 'photo'], default: 'text' },
  timestamp: { type: Date, default: Date.now },
});

// Indexes for Chat Messages
chatMessageSchema.index({ trip_id: 1 });
chatMessageSchema.index({ sender_id: 1, timestamp: -1 });

// Bookings Schema: Stores MakeMyTrip travel data
const bookingSchema = new Schema({
  type: { type: String, enum: ['flight', 'hotel', 'package', 'activity'], required: true },
  details: {
    name: { type: String, required: true, trim: true },
    price: { type: Number, required: true, min: 0 },
    destination: { type: String, required: true, trim: true },
    activities: [{ type: String }], // e.g., ["rafting", "trekking"]
    duration: { type: String },
    url: { type: String }, // MakeMyTrip booking URL
  },
  source: { type: String, enum: ['makemytrip_api', 'scraped'], required: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

// Indexes for Bookings
bookingSchema.index({ type: 1 });
bookingSchema.index({ 'details.destination': 1 });
bookingSchema.index({ 'details.price': 1 });

// Update timestamp
bookingSchema.pre('save', function(next) {
  this.updated_at = Date.now();
  next();
});

// Create Models
const User = mongoose.model('User', userSchema);
const Trip = mongoose.model('Trip', tripSchema);
const EmergencyNumber = mongoose.model('EmergencyNumber', emergencyNumberSchema);
const ChatMessage = mongoose.model('ChatMessage', chatMessageSchema);
const Booking = mongoose.model('Booking', bookingSchema);

module.exports = {
  User,
  Trip,
  EmergencyNumber,
  ChatMessage,
  Booking,
};