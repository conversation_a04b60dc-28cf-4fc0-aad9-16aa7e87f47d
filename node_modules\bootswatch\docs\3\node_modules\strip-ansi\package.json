{"_args": [[{"raw": "strip-ansi@^0.3.0", "scope": null, "escapedName": "strip-ansi", "name": "strip-ansi", "rawSpec": "^0.3.0", "spec": ">=0.3.0 <0.4.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/chalk"]], "_from": "strip-ansi@>=0.3.0 <0.4.0", "_id": "strip-ansi@0.3.0", "_inCache": true, "_location": "/strip-ansi", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.4.9", "_phantomChildren": {}, "_requested": {"raw": "strip-ansi@^0.3.0", "scope": null, "escapedName": "strip-ansi", "name": "strip-ansi", "rawSpec": "^0.3.0", "spec": ">=0.3.0 <0.4.0", "type": "range"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "_shasum": "25f48ea22ca79187f3174a4db8759347bb126220", "_shrinkwrap": null, "_spec": "strip-ansi@^0.3.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-ansi": "cli.js"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "dependencies": {"ansi-regex": "^0.2.1"}, "description": "Strip ANSI escape codes", "devDependencies": {"mocha": "*"}, "directories": {}, "dist": {"shasum": "25f48ea22ca79187f3174a4db8759347bb126220", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "cli.js"], "homepage": "https://github.com/sindresorhus/strip-ansi", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "strip-ansi", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi.git"}, "scripts": {"test": "mocha"}, "version": "0.3.0"}