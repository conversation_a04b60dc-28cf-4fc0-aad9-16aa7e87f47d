{"_args": [[{"raw": "sshpk@^1.7.0", "scope": null, "escapedName": "sshpk", "name": "sshpk", "rawSpec": "^1.7.0", "spec": ">=1.7.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/http-signature"]], "_from": "sshpk@>=1.7.0 <2.0.0", "_id": "sshpk@1.10.2", "_inCache": true, "_location": "/sshpk", "_nodeVersion": "0.12.15", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sshpk-1.10.2.tgz_1484356342573_0.6690851037856191"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.10", "_phantomChildren": {}, "_requested": {"raw": "sshpk@^1.7.0", "scope": null, "escapedName": "sshpk", "name": "sshpk", "rawSpec": "^1.7.0", "spec": ">=1.7.0 <2.0.0", "type": "range"}, "_requiredBy": ["/http-signature"], "_resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.2.tgz", "_shasum": "d5a804ce22695515638e798dbe23273de070a5fa", "_shrinkwrap": null, "_spec": "sshpk@^1.7.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/http-signature", "author": {"name": "Joyent, Inc"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jodid25519": "^1.0.0", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}, "description": "A library for finding and using SSH public keys", "devDependencies": {"benchmark": "^1.0.0", "sinon": "^1.17.2", "tape": "^3.5.0", "temp": "^0.8.2"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "d5a804ce22695515638e798dbe23273de070a5fa", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.2.tgz"}, "engines": {"node": ">=0.10.0"}, "gitHead": "3ae535658f83e610c0dd5bb97a1b2105db7220c7", "homepage": "https://github.com/arekinath/node-sshpk#readme", "license": "MIT", "main": "lib/index.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "name": "sshpk", "optionalDependencies": {"bcrypt-pbkdf": "^1.0.0", "ecc-jsbn": "~0.1.1", "jodid25519": "^1.0.0", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/arekinath/node-sshpk.git"}, "scripts": {"test": "tape test/*.js"}, "version": "1.10.2"}