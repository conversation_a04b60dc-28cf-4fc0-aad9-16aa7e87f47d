{"_args": [[{"raw": "sntp@1.x.x", "scope": null, "escapedName": "sntp", "name": "sntp", "rawSpec": "1.x.x", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/hawk"]], "_from": "sntp@>=1.0.0 <2.0.0", "_id": "sntp@1.0.9", "_inCache": true, "_location": "/sntp", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "_npmVersion": "1.4.23", "_phantomChildren": {}, "_requested": {"raw": "sntp@1.x.x", "scope": null, "escapedName": "sntp", "name": "sntp", "rawSpec": "1.x.x", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/hawk"], "_resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz", "_shasum": "6541184cc90aeea6c6e7b35e2659082443c66198", "_shrinkwrap": null, "_spec": "sntp@1.x.x", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/hawk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hueniverse.com"}, "bugs": {"url": "https://github.com/hueniverse/sntp/issues"}, "contributors": [], "dependencies": {"hoek": "2.x.x"}, "description": "SNTP Client", "devDependencies": {"lab": "4.x.x"}, "directories": {}, "dist": {"shasum": "6541184cc90aeea6c6e7b35e2659082443c66198", "tarball": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz"}, "engines": {"node": ">=0.8.0"}, "gitHead": "ee2e35284f684609990681734d39010cd356d7da", "homepage": "https://github.com/hueniverse/sntp", "keywords": ["sntp", "ntp", "time"], "licenses": [{"type": "BSD", "url": "http://github.com/hueniverse/sntp/raw/master/LICENSE"}], "main": "index", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "name": "sntp", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/hueniverse/sntp.git"}, "scripts": {"test": "make test-cov"}, "version": "1.0.9"}