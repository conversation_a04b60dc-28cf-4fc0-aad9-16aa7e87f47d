{"_args": [[{"raw": "redent@^1.0.0", "scope": null, "escapedName": "redent", "name": "redent", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/meow"]], "_from": "redent@>=1.0.0 <2.0.0", "_id": "redent@1.0.0", "_inCache": true, "_location": "/redent", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.14.4", "_phantomChildren": {}, "_requested": {"raw": "redent@^1.0.0", "scope": null, "escapedName": "redent", "name": "redent", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/meow"], "_resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "_shasum": "cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde", "_shrinkwrap": null, "_spec": "redent@^1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "dependencies": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}, "description": "Strip redundant indentation and indent the string", "devDependencies": {"ava": "*", "xo": "*"}, "directories": {}, "dist": {"shasum": "cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde", "tarball": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "97d9db1dbd5894fe1b14e01040da283b8d53409d", "homepage": "https://github.com/sindresorhus/redent", "keywords": ["string", "str", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "redent", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/redent.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}