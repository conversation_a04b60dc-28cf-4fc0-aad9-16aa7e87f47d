{"_args": [[{"raw": "repeat-element@^1.1.2", "scope": null, "escapedName": "repeat-element", "name": "repeat-element", "rawSpec": "^1.1.2", "spec": ">=1.1.2 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/braces"]], "_from": "repeat-element@>=1.1.2 <2.0.0", "_id": "repeat-element@1.1.2", "_inCache": true, "_location": "/repeat-element", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.5.1", "_phantomChildren": {}, "_requested": {"raw": "repeat-element@^1.1.2", "scope": null, "escapedName": "repeat-element", "name": "repeat-element", "rawSpec": "^1.1.2", "spec": ">=1.1.2 <2.0.0", "type": "range"}, "_requiredBy": ["/braces", "/fill-range"], "_resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz", "_shasum": "ef089a178d1483baae4d93eb98b4f9e4e11d990a", "_shrinkwrap": null, "_spec": "repeat-element@^1.1.2", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/braces", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/repeat-element/issues"}, "dependencies": {}, "description": "Create an array by repeating the given value n times.", "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.0.0", "glob": "^5.0.5", "minimist": "^1.1.1", "mocha": "^2.2.4"}, "directories": {}, "dist": {"shasum": "ef089a178d1483baae4d93eb98b4f9e4e11d990a", "tarball": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "7a6b21d58eafcc44fc8de133c70a8398ee9fdd8d", "homepage": "https://github.com/jonschlinkert/repeat-element", "keywords": ["array", "element", "repeat", "string"], "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/repeat-element/blob/master/LICENSE"}, "main": "index.js", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "repeat-element", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/repeat-element.git"}, "scripts": {"test": "mocha"}, "version": "1.1.2"}