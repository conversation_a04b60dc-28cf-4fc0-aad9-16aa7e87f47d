{"_args": [[{"raw": "repeating@^2.0.0", "scope": null, "escapedName": "repeating", "name": "repeating", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/indent-string"]], "_from": "repeating@>=2.0.0 <3.0.0", "_id": "repeating@2.0.1", "_inCache": true, "_location": "/repeating", "_nodeVersion": "4.4.2", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/repeating-2.0.1.tgz_1460395966602_0.9193919147364795"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.8.6", "_phantomChildren": {}, "_requested": {"raw": "repeating@^2.0.0", "scope": null, "escapedName": "repeating", "name": "repeating", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/indent-string"], "_resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "_shasum": "5214c53a926d3552707527fbab415dbc08d06dda", "_shrinkwrap": null, "_spec": "repeating@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/indent-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/repeating/issues"}, "dependencies": {"is-finite": "^1.0.0"}, "description": "Repeat a string - fast", "devDependencies": {"ava": "*", "xo": "*"}, "directories": {}, "dist": {"shasum": "5214c53a926d3552707527fbab415dbc08d06dda", "tarball": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "be02bcaf9a674b3c155477b3bf282136bcf44770", "homepage": "https://github.com/sindresorhus/repeating#readme", "keywords": ["repeat", "string", "repeating", "str", "text", "fill", "pad"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "repeating", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/repeating.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1"}