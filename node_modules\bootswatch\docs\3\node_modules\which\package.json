{"_args": [[{"raw": "which@~1.0.5", "scope": null, "escapedName": "which", "name": "which", "rawSpec": "~1.0.5", "spec": ">=1.0.5 <1.1.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/grunt"]], "_from": "which@>=1.0.5 <1.1.0", "_id": "which@1.0.9", "_inCache": true, "_location": "/which", "_nodeVersion": "1.1.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "2.6.0", "_phantomChildren": {}, "_requested": {"raw": "which@~1.0.5", "scope": null, "escapedName": "which", "name": "which", "rawSpec": "~1.0.5", "spec": ">=1.0.5 <1.1.0", "type": "range"}, "_requiredBy": ["/grunt", "/grunt-legacy-util"], "_resolved": "https://registry.npmjs.org/which/-/which-1.0.9.tgz", "_shasum": "460c1da0f810103d0321a9b633af9e575e64486f", "_shrinkwrap": null, "_spec": "which@~1.0.5", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/grunt", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bin": {"which": "./bin/which"}, "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "dependencies": {}, "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "devDependencies": {}, "directories": {}, "dist": {"shasum": "460c1da0f810103d0321a9b633af9e575e64486f", "tarball": "https://registry.npmjs.org/which/-/which-1.0.9.tgz"}, "gitHead": "df3d52a0ecd5f366d550e0f14d67ca4d5e621bad", "homepage": "https://github.com/isaacs/node-which", "license": "ISC", "main": "which.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "which", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "scripts": {}, "version": "1.0.9"}