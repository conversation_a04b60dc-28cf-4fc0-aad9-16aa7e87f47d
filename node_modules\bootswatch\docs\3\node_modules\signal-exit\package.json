{"_args": [[{"raw": "signal-exit@^3.0.0", "scope": null, "escapedName": "signal-exit", "name": "signal-exit", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/loud-rejection"]], "_from": "signal-exit@>=3.0.0 <4.0.0", "_id": "signal-exit@3.0.2", "_inCache": true, "_location": "/signal-exit", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/signal-exit-3.0.2.tgz_1480821660838_0.6809983775019646"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.9", "_phantomChildren": {}, "_requested": {"raw": "signal-exit@^3.0.0", "scope": null, "escapedName": "signal-exit", "name": "signal-exit", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "_requiredBy": ["/gauge", "/loud-rejection"], "_resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "_shasum": "b5fdc08f1287ea1178628e415e25132b73646c6d", "_shrinkwrap": null, "_spec": "signal-exit@^3.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/loud-rejection", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "dependencies": {}, "description": "when you want to fire an event no matter how a process exits.", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.10", "nyc": "^8.1.0", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^8.0.1"}, "directories": {}, "dist": {"shasum": "b5fdc08f1287ea1178628e415e25132b73646c6d", "tarball": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz"}, "files": ["index.js", "signals.js"], "gitHead": "9c5ad9809fe6135ef22e2623989deaffe2a4fa8a", "homepage": "https://github.com/tapjs/signal-exit", "keywords": ["signal", "exit"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "name": "signal-exit", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "tap --timeout=240 ./test/*.js --cov"}, "version": "3.0.2"}