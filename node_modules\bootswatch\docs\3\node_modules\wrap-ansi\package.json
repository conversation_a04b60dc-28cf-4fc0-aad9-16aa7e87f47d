{"_args": [[{"raw": "wrap-ansi@^2.0.0", "scope": null, "escapedName": "wrap-ansi", "name": "wrap-ansi", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/cliui"]], "_from": "wrap-ansi@>=2.0.0 <3.0.0", "_id": "wrap-ansi@2.1.0", "_inCache": true, "_location": "/wrap-ansi", "_nodeVersion": "4.6.2", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/wrap-ansi-2.1.0.tgz_1480440082575_0.23112521297298372"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.15.11", "_phantomChildren": {}, "_requested": {"raw": "wrap-ansi@^2.0.0", "scope": null, "escapedName": "wrap-ansi", "name": "wrap-ansi", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/cliui"], "_resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "_shasum": "d8fc3d284dd05794fe84973caecdd1cf824fdd85", "_shrinkwrap": null, "_spec": "wrap-ansi@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/cliui", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "description": "Wordwrap a string with ANSI escape codes", "devDependencies": {"ava": "^0.16.0", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^6.2.1", "strip-ansi": "^3.0.0", "xo": "*"}, "directories": {}, "dist": {"shasum": "d8fc3d284dd05794fe84973caecdd1cf824fdd85", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "a731af5a3461d92f2af302e81e05ea698a3c8c1a", "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "wrap-ansi", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "2.1.0"}