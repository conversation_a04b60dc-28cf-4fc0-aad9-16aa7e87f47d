{"_args": [[{"raw": "xtend@^4.0.0", "scope": null, "escapedName": "xtend", "name": "xtend", "rawSpec": "^4.0.0", "spec": ">=4.0.0 <5.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/is-my-json-valid"]], "_from": "xtend@>=4.0.0 <5.0.0", "_id": "xtend@4.0.1", "_inCache": true, "_location": "/xtend", "_nodeVersion": "0.10.32", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.14.1", "_phantomChildren": {}, "_requested": {"raw": "xtend@^4.0.0", "scope": null, "escapedName": "xtend", "name": "xtend", "rawSpec": "^4.0.0", "spec": ">=4.0.0 <5.0.0", "type": "range"}, "_requiredBy": ["/is-my-json-valid"], "_resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "_shasum": "a5c6d532be656e23db820efb943a1f04998d63af", "_shrinkwrap": null, "_spec": "xtend@^4.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/is-my-json-valid", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/xtend/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {}, "description": "extend like a boss", "devDependencies": {"tape": "~1.1.0"}, "directories": {}, "dist": {"shasum": "a5c6d532be656e23db820efb943a1f04998d63af", "tarball": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz"}, "engines": {"node": ">=0.4"}, "gitHead": "23dc302a89756da89c1897bc732a752317e35390", "homepage": "https://github.com/Raynos/xtend", "keywords": ["extend", "merge", "options", "opts", "object", "array"], "license": "MIT", "main": "immutable", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "xtend", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/Raynos/xtend.git"}, "scripts": {"test": "node test"}, "testling": {"files": "test.js", "browsers": ["ie/7..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "version": "4.0.1"}