{"_args": [[{"raw": "request@>=2.12.0", "scope": null, "escapedName": "request", "name": "request", "rawSpec": ">=2.12.0", "spec": ">=2.12.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/less"]], "_from": "request@>=2.12.0", "_id": "request@2.79.0", "_inCache": true, "_location": "/request", "_nodeVersion": "6.3.1", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/request-2.79.0.tgz_1479489666177_0.7806831058114767"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "_npmVersion": "2.15.9", "_phantomChildren": {}, "_requested": {"raw": "request@>=2.12.0", "scope": null, "escapedName": "request", "name": "request", "rawSpec": ">=2.12.0", "spec": ">=2.12.0", "type": "range"}, "_requiredBy": ["/less"], "_resolved": "https://registry.npmjs.org/request/-/request-2.79.0.tgz", "_shasum": "4dfe5bf6be8b8cdc37fcf93e04b65577722710de", "_shrinkwrap": null, "_spec": "request@>=2.12.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/less", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/request/request/issues"}, "dependencies": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "caseless": "~0.11.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~2.1.1", "har-validator": "~2.0.6", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "qs": "~6.3.0", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "uuid": "^3.0.0"}, "description": "Simplified HTTP request client.", "devDependencies": {"bluebird": "^3.2.1", "browserify": "^13.0.1", "browserify-istanbul": "^2.0.0", "buffer-equal": "^1.0.0", "codecov": "^1.0.1", "coveralls": "^2.11.4", "eslint": "^2.5.3", "function-bind": "^1.0.2", "istanbul": "^0.4.0", "karma": "^1.1.1", "karma-browserify": "^5.0.1", "karma-cli": "^1.0.0", "karma-coverage": "^1.0.0", "karma-phantomjs-launcher": "^1.0.0", "karma-tap": "^3.0.1", "phantomjs-prebuilt": "^2.1.3", "rimraf": "^2.2.8", "server-destroy": "^1.0.1", "tape": "^4.6.0", "taper": "^0.5.0"}, "directories": {}, "dist": {"shasum": "4dfe5bf6be8b8cdc37fcf93e04b65577722710de", "tarball": "https://registry.npmjs.org/request/-/request-2.79.0.tgz"}, "engines": {"node": ">= 4"}, "files": ["lib/", "index.js", "request.js"], "gitHead": "ff729c6f1a87237060d075908563ce13386395ac", "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "homepage": "https://github.com/request/request#readme", "license": "Apache-2.0", "main": "index.js", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "name": "request", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/request/request.git"}, "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-browser": "node tests/browser/start.js", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js"}, "tags": ["http", "simple", "util", "utility"], "version": "2.79.0", "warnings": [{"code": "ENOTSUP", "required": {"node": ">= 4"}, "pkgid": "request@2.79.0"}]}