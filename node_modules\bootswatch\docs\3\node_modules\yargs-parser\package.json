{"_args": [[{"raw": "yargs-parser@^5.0.0", "scope": null, "escapedName": "yargs-parser", "name": "yargs-parser", "rawSpec": "^5.0.0", "spec": ">=5.0.0 <6.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/yargs"]], "_from": "yargs-parser@>=5.0.0 <6.0.0", "_id": "yargs-parser@5.0.0", "_inCache": true, "_location": "/yargs-parser", "_nodeVersion": "0.10.48", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yargs-parser-5.0.0.tgz_1487447930908_0.674228576477617"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "2.15.1", "_phantomChildren": {}, "_requested": {"raw": "yargs-parser@^5.0.0", "scope": null, "escapedName": "yargs-parser", "name": "yargs-parser", "rawSpec": "^5.0.0", "spec": ">=5.0.0 <6.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.0.tgz", "_shasum": "275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a", "_shrinkwrap": null, "_spec": "yargs-parser@^5.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dependencies": {"camelcase": "^3.0.0"}, "description": "the mighty option parser used by yargs", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.12", "mocha": "^3.0.1", "nyc": "^10.0.0", "standard": "^8.0.0", "standard-version": "^4.0.0"}, "directories": {}, "dist": {"shasum": "275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.0.tgz"}, "files": ["lib", "index.js"], "gitHead": "2c95ba9e5ad3b8bb6248bf41f013d9bd3700d56f", "homepage": "https://github.com/yargs/yargs-parser#readme", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "name": "yargs-parser", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc mocha test/*.js"}, "version": "5.0.0"}