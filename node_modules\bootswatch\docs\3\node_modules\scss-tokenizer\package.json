{"_args": [[{"raw": "scss-tokenizer@^0.2.3", "scope": null, "escapedName": "scss-tokenizer", "name": "scss-tokenizer", "rawSpec": "^0.2.3", "spec": ">=0.2.3 <0.3.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/sass-graph"]], "_from": "scss-tokenizer@>=0.2.3 <0.3.0", "_id": "scss-tokenizer@0.2.3", "_inCache": true, "_location": "/scss-tokenizer", "_nodeVersion": "7.6.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/scss-tokenizer-0.2.3.tgz_1494600519250_0.10013756598345935"}, "_npmUser": {"name": "xzyfer", "email": "<EMAIL>"}, "_npmVersion": "4.1.2", "_phantomChildren": {"amdefine": "1.0.1"}, "_requested": {"raw": "scss-tokenizer@^0.2.3", "scope": null, "escapedName": "scss-tokenizer", "name": "scss-tokenizer", "rawSpec": "^0.2.3", "spec": ">=0.2.3 <0.3.0", "type": "range"}, "_requiredBy": ["/sass-graph"], "_resolved": "https://registry.npmjs.org/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz", "_shasum": "8eb06db9a9723333824d3f5530641149847ce5d1", "_shrinkwrap": null, "_spec": "scss-tokenizer@^0.2.3", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/sass-graph", "author": {"name": "xzyfer"}, "bugs": {"url": "https://github.com/sasstools/scss-tokenizer/issues"}, "dependencies": {"js-base64": "^2.1.8", "source-map": "^0.4.2"}, "description": "A tokenzier for Sass' SCSS syntax", "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-es2015": "^6.24.1", "chai": "^3.5.0", "glob": "^5.0.10", "mocha": "^3.2.0", "sass-spec": "^3.5.0-1"}, "directories": {}, "dist": {"shasum": "8eb06db9a9723333824d3f5530641149847ce5d1", "tarball": "https://registry.npmjs.org/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz"}, "files": ["index.js", "lib"], "gitHead": "69a031e614fd948233c0583a29a3e764c2f90bc9", "homepage": "https://github.com/sasstools/scss-tokenizer", "keywords": ["parser", "tokenizer", "sass", "scss", "libsass"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "xzyfer", "email": "<EMAIL>"}], "name": "scss-tokenizer", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sasstools/scss-tokenizer.git"}, "scripts": {"build": "npm run clean; babel src/ --out-dir lib", "clean": "rm lib/*", "test": "mocha"}, "version": "0.2.3"}