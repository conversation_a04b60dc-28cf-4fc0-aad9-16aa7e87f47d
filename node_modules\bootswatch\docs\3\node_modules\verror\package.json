{"_args": [[{"raw": "verror@1.3.6", "scope": null, "escapedName": "verror", "name": "verror", "rawSpec": "1.3.6", "spec": "1.3.6", "type": "version"}, "/Users/<USER>/Development/github/bootswatch/node_modules/jsprim"]], "_from": "verror@1.3.6", "_id": "verror@1.3.6", "_inCache": true, "_location": "/verror", "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "_npmVersion": "1.1.65", "_phantomChildren": {}, "_requested": {"raw": "verror@1.3.6", "scope": null, "escapedName": "verror", "name": "verror", "rawSpec": "1.3.6", "spec": "1.3.6", "type": "version"}, "_requiredBy": ["/jsprim"], "_resolved": "https://registry.npmjs.org/verror/-/verror-1.3.6.tgz", "_shasum": "cff5df12946d297d2baaefaa2689e25be01c005c", "_shrinkwrap": null, "_spec": "verror@1.3.6", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/jsprim", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dependencies": {"extsprintf": "1.0.2"}, "description": "richer JavaScript errors", "devDependencies": {}, "directories": {}, "dist": {"shasum": "cff5df12946d297d2baaefaa2689e25be01c005c", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.6.tgz"}, "engines": ["node >=0.6.0"], "homepage": "https://github.com/davepacheco/node-verror#readme", "main": "./lib/verror.js", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "name": "verror", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/davepacheco/node-verror.git"}, "scripts": {"test": "make test"}, "version": "1.3.6"}