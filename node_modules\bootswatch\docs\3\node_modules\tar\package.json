{"_args": [[{"raw": "tar@^2.0.0", "scope": null, "escapedName": "tar", "name": "tar", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/node-gyp"]], "_from": "tar@>=2.0.0 <3.0.0", "_id": "tar@2.2.1", "_inCache": true, "_location": "/tar", "_nodeVersion": "2.2.2", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "_npmVersion": "2.14.3", "_phantomChildren": {}, "_requested": {"raw": "tar@^2.0.0", "scope": null, "escapedName": "tar", "name": "tar", "rawSpec": "^2.0.0", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/node-gyp"], "_resolved": "https://registry.npmjs.org/tar/-/tar-2.2.1.tgz", "_shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "_shrinkwrap": null, "_spec": "tar@^2.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/node-gyp", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "description": "tar for node", "devDependencies": {"graceful-fs": "^4.1.2", "mkdirp": "^0.5.0", "rimraf": "1.x", "tap": "0.x"}, "directories": {}, "dist": {"shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "tarball": "https://registry.npmjs.org/tar/-/tar-2.2.1.tgz"}, "gitHead": "52237e39d2eb68d22a32d9a98f1d762189fe6a3d", "homepage": "https://github.com/isaacs/node-tar#readme", "license": "ISC", "main": "tar.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "name": "tar", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "scripts": {"test": "tap test/*.js"}, "version": "2.2.1"}