{"_args": [[{"raw": "trim-newlines@^1.0.0", "scope": null, "escapedName": "trim-newlines", "name": "trim-newlines", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/meow"]], "_from": "trim-newlines@>=1.0.0 <2.0.0", "_id": "trim-newlines@1.0.0", "_inCache": true, "_location": "/trim-newlines", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.14.4", "_phantomChildren": {}, "_requested": {"raw": "trim-newlines@^1.0.0", "scope": null, "escapedName": "trim-newlines", "name": "trim-newlines", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/meow"], "_resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "_shasum": "5887966bb582a4503a41eb524f7d35011815a613", "_shrinkwrap": null, "_spec": "trim-newlines@^1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/trim-newlines/issues"}, "dependencies": {}, "description": "Trim newlines from the start and/or end of a string", "devDependencies": {"ava": "*", "xo": "*"}, "directories": {}, "dist": {"shasum": "5887966bb582a4503a41eb524f7d35011815a613", "tarball": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "f651a2d4cbf382c2936e6e53edee9316602e4ce7", "homepage": "https://github.com/sindresorhus/trim-newlines", "keywords": ["trim", "newline", "newlines", "linebreak", "lf", "crlf", "left", "right", "start", "end", "string", "str", "remove", "delete", "strip"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "trim-newlines", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/trim-newlines.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}