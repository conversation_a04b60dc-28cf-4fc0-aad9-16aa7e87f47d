{"_args": [[{"raw": "require-main-filename@^1.0.1", "scope": null, "escapedName": "require-main-filename", "name": "require-main-filename", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/yargs"]], "_from": "require-main-filename@>=1.0.1 <2.0.0", "_id": "require-main-filename@1.0.1", "_inCache": true, "_location": "/require-main-filename", "_nodeVersion": "3.2.0", "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/require-main-filename-1.0.1.tgz_1455688492890_0.0750324921682477"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "3.3.0", "_phantomChildren": {}, "_requested": {"raw": "require-main-filename@^1.0.1", "scope": null, "escapedName": "require-main-filename", "name": "require-main-filename", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz", "_shasum": "97f717b69d48784f5f526a6c5aa8ffdda055a4d1", "_shrinkwrap": null, "_spec": "require-main-filename@^1.0.1", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/require-main-filename/issues"}, "dependencies": {}, "description": "shim for require.main.filename() that works in as many environments as possible", "devDependencies": {"chai": "^3.5.0", "standard": "^6.0.5", "tap": "^5.2.0"}, "directories": {}, "dist": {"shasum": "97f717b69d48784f5f526a6c5aa8ffdda055a4d1", "tarball": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz"}, "gitHead": "6dd2291332bed764c56302ccdd14da8a213249a1", "homepage": "https://github.com/yargs/require-main-filename#readme", "keywords": ["require", "shim", "iisnode"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "name": "require-main-filename", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/yargs/require-main-filename.git"}, "scripts": {"pretest": "standard", "test": "tap --coverage test.js"}, "version": "1.0.1"}