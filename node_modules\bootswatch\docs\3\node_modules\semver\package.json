{"_args": [[{"raw": "semver@2 || 3 || 4 || 5", "scope": null, "escapedName": "semver", "name": "semver", "rawSpec": "2 || 3 || 4 || 5", "spec": ">=2.0.0 <3.0.0||>=3.0.0 <4.0.0||>=4.0.0 <5.0.0||>=5.0.0 <6.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-package-data"]], "_from": "semver@>=2.0.0 <3.0.0||>=3.0.0 <4.0.0||>=4.0.0 <5.0.0||>=5.0.0 <6.0.0", "_id": "semver@5.4.1", "_inCache": true, "_location": "/semver", "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver-5.4.1.tgz_1500922107643_0.5125251261051744"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "5.3.0", "_phantomChildren": {}, "_requested": {"raw": "semver@2 || 3 || 4 || 5", "scope": null, "escapedName": "semver", "name": "semver", "rawSpec": "2 || 3 || 4 || 5", "spec": ">=2.0.0 <3.0.0||>=3.0.0 <4.0.0||>=4.0.0 <5.0.0||>=5.0.0 <6.0.0", "type": "range"}, "_requiredBy": ["/normalize-package-data"], "_resolved": "https://registry.npmjs.org/semver/-/semver-5.4.1.tgz", "_shasum": "e059c09d8571f0540823733433505d3a2f00b18e", "_shrinkwrap": null, "_spec": "semver@2 || 3 || 4 || 5", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/normalize-package-data", "bin": {"semver": "./bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "dependencies": {}, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^10.7.0"}, "directories": {}, "dist": {"integrity": "sha512-WfG/X9+oATh81XtllIo/I8gOiY9EXRdv1cQdyykeXK17YcUW3EXUAi2To4pcH6nZtJPr7ZOpM5OMyWJZm+8Rsg==", "shasum": "e059c09d8571f0540823733433505d3a2f00b18e", "tarball": "https://registry.npmjs.org/semver/-/semver-5.4.1.tgz"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "0877c942a6af00edcda5c16fdd934684e1b20a1c", "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "name": "semver", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"test": "tap test/*.js --cov -J"}, "version": "5.4.1"}