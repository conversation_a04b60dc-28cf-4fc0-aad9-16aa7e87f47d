{"Commands:": "Opdrachten:", "Options:": "Opties:", "Examples:": "Voorbeelden:", "boolean": "boolean", "count": "aantal", "string": "text", "number": "nummer", "array": "lijst", "required": "ve<PERSON><PERSON>t", "default:": "standaard:", "choices:": "keuzes:", "aliases:": "aliassen:", "generated-value": "gegener<PERSON><PERSON> waarde", "Not enough non-option arguments: got %s, need at least %s": "<PERSON>et genoeg non-optie argumenten. Gekregen: %s, minstens nodig: %s", "Too many non-option arguments: got %s, maximum of %s": "Te veel non-optie argumenten. Gekregen: %s, maximum: %s", "Missing argument value: %s": {"one": "Missing argument value: %s", "other": "Missing argument values: %s"}, "Missing required argument: %s": {"one": "Missend verplichte argument: %s", "other": "Missende verplichte argumenten: %s"}, "Unknown argument: %s": {"one": "Onbekend argument: %s", "other": "Onbekende argumenten: %s"}, "Invalid values:": "Ongeldige waardes:", "Argument: %s, Given: %s, Choices: %s": "Argument: %s, G<PERSON>ven: %s, Keuzes: %s", "Argument check failed: %s": "Argument check mislukt: %s", "Implications failed:": "Implicaties mislukt:", "Not enough arguments following: %s": "<PERSON>et genoeg argumenten na: %s", "Invalid JSON config file: %s": "Ongeldig JSON configuratiebestand: %s", "Path to JSON config file": "Pad naar JSON configuratiebestand", "Show help": "Toon help", "Show version number": "Toon versie nummer", "Did you mean %s?": "Bedoelde u misschien %s?"}