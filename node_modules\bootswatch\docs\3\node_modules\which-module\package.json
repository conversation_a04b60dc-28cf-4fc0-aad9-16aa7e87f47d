{"_args": [[{"raw": "which-module@^1.0.0", "scope": null, "escapedName": "which-module", "name": "which-module", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/yargs"]], "_from": "which-module@>=1.0.0 <2.0.0", "_id": "which-module@1.0.0", "_inCache": true, "_location": "/which-module", "_nodeVersion": "5.11.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/which-module-1.0.0.tgz_1465192451403_0.19380306638777256"}, "_npmUser": {"name": "nexdrew", "email": "<EMAIL>"}, "_npmVersion": "3.8.8", "_phantomChildren": {}, "_requested": {"raw": "which-module@^1.0.0", "scope": null, "escapedName": "which-module", "name": "which-module", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz", "_shasum": "bba63ca861948994ff307736089e3b96026c2a4f", "_shrinkwrap": null, "_spec": "which-module@^1.0.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/yargs", "author": {"name": "nexdrew"}, "bugs": {"url": "https://github.com/nexdrew/which-module/issues"}, "dependencies": {}, "description": "Find the module object for something that was require()d", "devDependencies": {"ava": "^0.15.2", "coveralls": "^2.11.9", "nyc": "^6.4.4", "standard": "^7.1.2", "standard-version": "^2.3.0"}, "directories": {}, "dist": {"shasum": "bba63ca861948994ff307736089e3b96026c2a4f", "tarball": "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz"}, "files": ["index.js"], "gitHead": "a5b7492798fac7e484fd8812d3d3a1138bb08784", "homepage": "https://github.com/nexdrew/which-module#readme", "keywords": ["which", "module", "exports", "filename", "require", "reverse", "lookup"], "license": "ISC", "main": "index.js", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}], "name": "which-module", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/nexdrew/which-module.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc ava"}, "version": "1.0.0"}