{"_args": [[{"raw": "require-from-string@^1.1.0", "scope": null, "escapedName": "require-from-string", "name": "require-from-string", "rawSpec": "^1.1.0", "spec": ">=1.1.0 <2.0.0", "type": "range"}, "/Users/<USER>/Development/github/bootswatch/node_modules/cosmiconfig"]], "_from": "require-from-string@>=1.1.0 <2.0.0", "_id": "require-from-string@1.2.1", "_inCache": true, "_location": "/require-from-string", "_nodeVersion": "6.6.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/require-from-string-1.2.1.tgz_1475350323439_0.3740670408587903"}, "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "_npmVersion": "3.10.3", "_phantomChildren": {}, "_requested": {"raw": "require-from-string@^1.1.0", "scope": null, "escapedName": "require-from-string", "name": "require-from-string", "rawSpec": "^1.1.0", "spec": ">=1.1.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cosmiconfig"], "_resolved": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.1.tgz", "_shasum": "529c9ccef27380adfec9a2f965b649bbee636418", "_shrinkwrap": null, "_spec": "require-from-string@^1.1.0", "_where": "/Users/<USER>/Development/github/bootswatch/node_modules/cosmiconfig", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "dependencies": {}, "description": "Require module from string", "devDependencies": {"mocha": "*"}, "directories": {}, "dist": {"shasum": "529c9ccef27380adfec9a2f965b649bbee636418", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.1.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "b81e995c6ff82fbf71d9ee7a9990b10794fecb98", "homepage": "https://github.com/floatdrop/require-from-string#readme", "keywords": [], "license": "MIT", "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "name": "require-from-string", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "scripts": {"test": "mocha"}, "version": "1.2.1"}